/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : PerfLogService.java
 * Project     : SPM Platform
 */
package com.siemens.spm.perflog.strategy;

import com.siemens.spm.datahub.api.boundary.DataIntegrationService;
import com.siemens.spm.datahub.api.vo.DataHubPerfLogEventVO;
import com.siemens.spm.datahub.api.vo.response.DataHubMissingDataResponseVO;
import com.siemens.spm.datahub.api.vo.response.DataHubPerfLogChunkResponseVO;
import com.siemens.spm.datahub.api.vo.response.DataHubPerfLogDisabledDataResponseVO;
import com.siemens.spm.datahub.exception.DataHubException;
import com.siemens.spm.perflog.boundary.DirectPerfLogRetriever;
import com.siemens.spm.perflog.utils.PerfLogConverter;
import com.siemens.spm.perflog.vo.PerfLogBundleVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;
import com.siemens.spm.perflog.vo.PerfLogGapVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;

@Slf4j
public class DirectPerfLogRetrieverBean implements DirectPerfLogRetriever {

    /**
     * Require spm-datahub-sdk
     */
    @Autowired
    private DataIntegrationService dataHubSpmService;

    private static final String GET_PERFLOG_ERROR_MESSAGE = "Error when getting perflog data from Data Hub for Agency:{}, Intersection:{}, from_time:{}, to_time:{} ";

    private static final String GET_MISSING_PERFLOG_ERROR_MESSAGE = "Error when getting missing perflog data from Data Hub for Agency:{}, Intersection:{}, from_time:{}, to_time:{} ";

    /**
     * Get PerfLog from Data Hub
     */
    @Override
    public PerfLogBundleVO getPerfLog(Integer agencyId, String intUUID, LocalDateTime fromTime, LocalDateTime toTime) {
        if (agencyId == null || intUUID == null || fromTime == null || toTime == null) {
            throw new IllegalArgumentException();
        }

        DataHubPerfLogChunkResponseVO dhResponse;
        try {
            dhResponse = dataHubSpmService.getPerfLog(agencyId, intUUID, fromTime, toTime);
        } catch (DataHubException e) {
            log.error(GET_PERFLOG_ERROR_MESSAGE, agencyId, intUUID, fromTime, toTime);
            return null;
        }

        return getPerfLogBundleVO(intUUID, fromTime, toTime, dhResponse);
    }

    @Override
    public PerfLogBundleVO getPerfLogAllowNoData(Integer agencyId,
                                                 String intUUID,
                                                 LocalDateTime fromTime,
                                                 LocalDateTime toTime) {
        if (agencyId == null || intUUID == null || fromTime == null || toTime == null) {
            throw new IllegalArgumentException();
        }

        DataHubPerfLogChunkResponseVO dhResponse;
        try {
            dhResponse = dataHubSpmService.getPerfLogAllowNoData(agencyId, intUUID, fromTime, toTime);
        } catch (DataHubException e) {
            log.error(GET_PERFLOG_ERROR_MESSAGE, agencyId, intUUID, fromTime, toTime);
            return null;
        }

        return getPerfLogBundleVO(intUUID, fromTime, toTime, dhResponse);
    }

    /**
     * Get PerfLog gaps from Data Hub
     *
     * @return List of PerfLogGapVO or null if errors occur
     */
    @SuppressWarnings("java:S1168")
    @Override
    public List<PerfLogGapVO> getPerfLogGap(Integer agencyId,
                                            String intUUID,
                                            LocalDateTime fromTime,
                                            LocalDateTime toTime) {
        Instant before = Instant.now(); // APP_DEBUG

        DataHubMissingDataResponseVO missingDataResponseVO;
        DataHubPerfLogDisabledDataResponseVO disabledHistoryDataResponseVO;
        try {
            missingDataResponseVO = dataHubSpmService.getMissingData(agencyId, intUUID, fromTime, toTime);
            disabledHistoryDataResponseVO = dataHubSpmService.getIntersectionStatusHistory(agencyId, intUUID, fromTime,
                    toTime);
        } catch (DataHubException e) {
            log.error(GET_MISSING_PERFLOG_ERROR_MESSAGE,
                    agencyId,
                    intUUID,
                    fromTime,
                    toTime);
            return null;
        }

        List<PerfLogGapVO> perfLogGapList = PerfLogConverter.createPerfLogGapListFromMissingAndDisableGap(
                missingDataResponseVO,
                disabledHistoryDataResponseVO);

        Instant after = Instant.now(); // APP_DEBUG
        Duration duration = Duration.between(before, after);
        String debugMessage = String.format(
                "METHOD=%s | PERFLOG_INTERVAL=%d | GAP_COUNT=%d | RUNTIME=%d.%09d",
                "getPerfLogGap",
                Duration.between(fromTime, toTime).getSeconds(),
                perfLogGapList.size(),
                duration.getSeconds(), duration.getNano());
        log.debug(debugMessage);

        return perfLogGapList;
    }

    @Override
    public PerfLogEventVO getLastCoordPatternChanged(Integer agencyId,
                                                     String intUUID,
                                                     LocalDateTime fromTime,
                                                     LocalDateTime toTime) throws DataHubException {
        if (agencyId == null || intUUID == null || fromTime == null || toTime == null) {
            throw new IllegalArgumentException();
        }

        DataHubPerfLogEventVO dhResponse;
        try {
            dhResponse = dataHubSpmService.getLastCoordPatternChanged(agencyId, intUUID, fromTime, toTime);
        } catch (DataHubException e) {
            log.error("Error get last coord pattern changed for Agency:{}, Intersection:{}, from_time:{}, to_time:{} ",
                    agencyId, intUUID, fromTime, toTime);
            throw new DataHubException(e);
        }

        return PerfLogConverter.convertPerfLogEventVO(dhResponse);
    }

    @Override
    public PerfLogBundleVO getPerfLog(Integer agencyId,
                                      String intUUID,
                                      LocalDateTime fromTime,
                                      LocalDateTime toTime,
                                      List<Integer> eventTypes) {
        if (agencyId == null || intUUID == null || fromTime == null || toTime == null) {
            throw new IllegalArgumentException();
        }

        DataHubPerfLogChunkResponseVO dhResponse;
        try {
            dhResponse = dataHubSpmService.getPerfLog(agencyId, intUUID, fromTime, toTime, eventTypes);
        } catch (DataHubException e) {
            log.error(GET_PERFLOG_ERROR_MESSAGE, agencyId, intUUID, fromTime, toTime);
            return null;
        }

        return getPerfLogBundleVO(intUUID, fromTime, toTime, dhResponse);
    }

    private static PerfLogBundleVO getPerfLogBundleVO(String intUUID,
                                                      LocalDateTime fromTime,
                                                      LocalDateTime toTime,
                                                      DataHubPerfLogChunkResponseVO dhResponse) {
        Instant before = Instant.now(); // APP_DEBUG

        // Return PerfLog bundle
        PerfLogBundleVO perfLogBundleVO = PerfLogConverter.convertPerfLogChunk(dhResponse, intUUID, fromTime, toTime);

        Instant after = Instant.now(); // APP_DEBUG
        Duration duration = Duration.between(before, after);
        String debugMessage = String.format(
                "METHOD=%s | PERFLOG_INTERVAL=%d | EVENT_COUNT=%d | RUNTIME=%d.%09d",
                "getPerfLog",
                Duration.between(fromTime, toTime).getSeconds(),
                perfLogBundleVO != null ? perfLogBundleVO.getEventCount() : 0,
                duration.getSeconds(), duration.getNano());
        log.debug(debugMessage);

        return perfLogBundleVO;
    }

}
