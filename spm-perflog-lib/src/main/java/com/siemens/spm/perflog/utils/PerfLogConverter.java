/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : PerfLogConverter.java
 * Project     : SPM Platform
 */
package com.siemens.spm.perflog.utils;

import com.siemens.spm.datahub.api.vo.DataHubConfigDataVO;
import com.siemens.spm.datahub.api.vo.DataHubDetectorInfoVO;
import com.siemens.spm.datahub.api.vo.DataHubIntersectionConfigVO;
import com.siemens.spm.datahub.api.vo.DataHubIntersectionInfoVO;
import com.siemens.spm.datahub.api.vo.DataHubLocationVO;
import com.siemens.spm.datahub.api.vo.DataHubPatternInfoVO;
import com.siemens.spm.datahub.api.vo.DataHubPerfLogChunkVO;
import com.siemens.spm.datahub.api.vo.DataHubPerfLogEventVO;
import com.siemens.spm.datahub.api.vo.DataHubRingStructureInfoVO;
import com.siemens.spm.datahub.api.vo.DataHubSequenceInfoVO;
import com.siemens.spm.datahub.api.vo.PerfLogDisableVo;
import com.siemens.spm.datahub.api.vo.response.DataHubMissingDataResponseVO;
import com.siemens.spm.datahub.api.vo.response.DataHubPerfLogChunkResponseVO;
import com.siemens.spm.datahub.api.vo.response.DataHubPerfLogDisabledDataResponseVO;
import com.siemens.spm.perflog.domaintype.GapType;
import com.siemens.spm.perflog.vo.DetectorInfoVO;
import com.siemens.spm.perflog.vo.IntersectionConfigVO;
import com.siemens.spm.perflog.vo.IntersectionInfoVO;
import com.siemens.spm.perflog.vo.LocationVO;
import com.siemens.spm.perflog.vo.PatternInfoVO;
import com.siemens.spm.perflog.vo.PerfLogBundleVO;
import com.siemens.spm.perflog.vo.PerfLogChunkVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;
import com.siemens.spm.perflog.vo.PerfLogGapVO;
import com.siemens.spm.perflog.vo.RingStructureInfoVO;
import com.siemens.spm.perflog.vo.SequenceInfoVO;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;

/**
 * MaiPH: Update UT
 */
public final class PerfLogConverter {

    private PerfLogConverter() {
    }

    private static final int DEFAULT_GAP_HOURS = 1;

    private static final String DATETIME_FORMATTER = "yyyy-MM-dd HH:mm:ss";

    /**
     * Convert LocalDateTime to String to store in DB
     *
     * @param localDateTime
     * @return
     */
    public static String dateTimeToStr(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return null;
        }

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATETIME_FORMATTER);
        return formatter.format(localDateTime);
    }

    /**
     * Convert String to LocalDateTime to update VO
     *
     * @param dateTimeStr
     * @return
     */
    public static LocalDateTime strToDateTime(String dateTimeStr) {
        if (dateTimeStr == null) {
            return null;
        }

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATETIME_FORMATTER);
        return LocalDateTime.parse(dateTimeStr, formatter);
    }

    /**
     * Convert DataHubPerfLogChunkVO to PerfLogChunkVO
     */
    public static PerfLogBundleVO convertPerfLogChunk(DataHubPerfLogChunkResponseVO dhResponse,
                                                      String intUUID,
                                                      LocalDateTime fromTime,
                                                      LocalDateTime toTime) {
        if (intUUID == null || fromTime == null || toTime == null) {
            throw new IllegalArgumentException();
        }

        // Convert to PerfLogBundleVO
        ArrayList<PerfLogChunkVO> perfLogChunkVOList = new ArrayList<>();
        HashMap<String, IntersectionConfigVO> intConfigMap = new HashMap<>();

        if (dhResponse != null) {
            for (DataHubPerfLogChunkVO dhPerfLogChunkVO : dhResponse) {
                IntersectionConfigVO chunkIntConfig = convertIntConfigVO(intUUID, dhPerfLogChunkVO.getConfigId(),
                        dhPerfLogChunkVO.getConfigData());
                intConfigMap.put(chunkIntConfig.getConfigID(), chunkIntConfig);

                perfLogChunkVOList.add(convertPerfLogChunkVO(intUUID, dhPerfLogChunkVO));
            }
        }

        return PerfLogBundleVO.builder()
                .intUUID(intUUID)
                .fromTime(fromTime)
                .toTime(toTime)
                .intConfigs(intConfigMap)
                .perfLogChunks(perfLogChunkVOList)
                .build();
    }

    public static List<PerfLogGapVO> convertMissingData(DataHubMissingDataResponseVO dhResponse) {
        if (dhResponse == null) {
            throw new IllegalArgumentException();
        }

        List<LocalDateTime> missingList = dhResponse.getMissingTimes();
        if (missingList == null) {
            return Collections.emptyList();
        }

        ArrayList<PerfLogGapVO> perfLogGapVOList = new ArrayList<>();
        for (LocalDateTime missingData : missingList) {
            perfLogGapVOList.add(PerfLogGapVO.builder()
                    .fromTime(missingData)
                    .toTime(missingData.plusHours(DEFAULT_GAP_HOURS))
                    .build());
        }

        return perfLogGapVOList;
    }

    public static List<PerfLogGapVO> createPerfLogGapListFromMissingAndDisableGap(
            DataHubMissingDataResponseVO missingDataResponseVO,
            DataHubPerfLogDisabledDataResponseVO historyDataResponseVO
    ) {

        if (missingDataResponseVO == null) {
            throw new IllegalArgumentException("missingDataResponseVO is null");
        } else if (historyDataResponseVO == null) {
            throw new IllegalArgumentException("historyDataResponseVO is null");
        }

        List<LocalDateTime> missingList = Optional.ofNullable(missingDataResponseVO.getMissingTimes())
                .orElse(Collections.emptyList());

        List<PerfLogDisableVo> disabledRanges = Optional.ofNullable(historyDataResponseVO.getDisabledRanges())
                .orElse(Collections.emptyList());

        Stream<PerfLogGapVO> missingGapStream = missingList
                .stream().map(
                        missing -> PerfLogGapVO.builder()
                                .fromTime(missing)
                                .toTime(missing.plusHours(DEFAULT_GAP_HOURS))
                                .gapType(GapType.MISSING)
                                .build()
                );

        Stream<PerfLogGapVO> disabledGapStream = disabledRanges.stream().map(
                history -> PerfLogGapVO.builder()
                        .fromTime(history.getFromTime().toLocalDateTime())
                        .toTime(history.getToTime().toLocalDateTime())
                        .gapType(GapType.DISABLED)
                        .build()
        );

        List<PerfLogGapVO> missingGaps = missingGapStream.sorted(Comparator.comparing(PerfLogGapVO::getFromTime))
                .toList();
        List<PerfLogGapVO> disabledGaps = disabledGapStream.sorted(Comparator.comparing(PerfLogGapVO::getFromTime))
                .toList();

        if (missingGaps.isEmpty()) {
            return disabledGaps;
        }

        if (disabledGaps.isEmpty()) {
            return missingGaps;
        }

        return getMergedPerfLogGaps(missingGaps, disabledGaps);
    }

    /**
     * Update new from time and to time of PerfLogGap so Missing and Disabled gap doesn't overlap each other
     *
     * @param missingGaps  list of sorted missing gaps (by fromTime)
     * @param disabledGaps list of sorted disable gaps (by fromTime)
     * @return List<PerfLogGapVO> sorted combine list of gaps (by fromTime)
     */
    private static List<PerfLogGapVO> getMergedPerfLogGaps(List<PerfLogGapVO> missingGaps,
                                                           List<PerfLogGapVO> disabledGaps) {
        List<PerfLogGapVO> newMergedList = new ArrayList<>();
        int missingPointer = 0;
        int disabledPointer = 0;

        while (missingPointer < missingGaps.size() || disabledPointer < disabledGaps.size()) {

            if (disabledPointer == disabledGaps.size()) {
                newMergedList.add(missingGaps.get(missingPointer));
                missingPointer++;
                continue;
            }

            if (missingPointer == missingGaps.size()) {
                newMergedList.add(disabledGaps.get(disabledPointer));
                disabledPointer++;
                continue;
            }

            PerfLogGapVO currentMissingGap = missingGaps.get(missingPointer);
            PerfLogGapVO currentDisabledGap = disabledGaps.get(disabledPointer);

            LocalDateTime fM = currentMissingGap.getFromTime();
            LocalDateTime tM = currentMissingGap.getToTime();

            LocalDateTime fD = currentDisabledGap.getFromTime();
            LocalDateTime tD = currentDisabledGap.getToTime();

            //case 1: missing gap is entirely before disable
            if (!tM.isAfter(fD)) {
                newMergedList.add(currentMissingGap);
                missingPointer++;
                continue;
            }

            //case 2: missing gap is entirely after disable
            if (!fM.isBefore(tD)) {
                newMergedList.add(currentDisabledGap);
                disabledPointer++;
                continue;
            }

            //case 3: disable overlap missing gap entirely
            boolean disabledOverlapMissingEntirely = !fM.isBefore(fD) && !tM.isAfter(tD);

            if (disabledOverlapMissingEntirely) {
                missingPointer++;
                continue;
            }

            //case 4: disable overlap back of missing gap
            boolean disabledOverlapBackOfMissing = fM.isBefore(fD) && fD.isBefore(tM) && !tD.isBefore(tM);

            if (disabledOverlapBackOfMissing) {
                currentMissingGap.setToTime(fD);

                newMergedList.add(currentMissingGap);
                missingPointer++;

                continue;
            }

            //case 5: disable overlap head of missing gap
            boolean disabledOverlapHeadOfMissing = fM.isBefore(tD) && tD.isBefore(tM) && !fM.isBefore(fD);

            if (disabledOverlapHeadOfMissing) {
                currentMissingGap.setFromTime(tD);

                newMergedList.add(currentDisabledGap);
                newMergedList.add(currentMissingGap);
                missingPointer++;
                disabledPointer++;

                continue;
            }

            //case 6: missing overlap disable entirely
            boolean missingOverlapDisableEntirely = fM.isBefore(fD) && tD.isBefore(tM);

            if (missingOverlapDisableEntirely) {
                newMergedList.add(
                        currentMissingGap.toBuilder()
                                .toTime(fD).build()
                );
                newMergedList.add(currentDisabledGap);

                currentMissingGap.setFromTime(tD);
                disabledPointer++;
            }
        }

        return newMergedList;
    }

    /**
     * @param intUUID
     * @param configID
     * @param configData
     * @return
     */
    private static IntersectionConfigVO convertIntConfigVO(String intUUID,
                                                           String configID,
                                                           DataHubConfigDataVO configData) {
        if (intUUID == null || configID == null || configData == null) {
            throw new IllegalArgumentException();
        }

        DataHubIntersectionConfigVO dhIntConfigVO = configData.getConfig();

        return IntersectionConfigVO.builder()
                .configID(configID)
                .validFromTime(configData.getValidFromTime())
                .validToTime(configData.getValidToTime())
                .intInfo(convertIntInfo(dhIntConfigVO.getIntInfo()))
                .patternInfo(convertPatternInfo(dhIntConfigVO.getPatternInfo()))
                .detInfo(convertDetInfo(dhIntConfigVO.getDetectorInfo()))
                .sequenceInfo(convertSequenceInfo(dhIntConfigVO.getSequenceInfo()))
                .ringStructureInfo(convertRingStructureInfo(dhIntConfigVO.getRingStructureInfo()))
                .approaches(IntersectionConfigConverter.convertApproaches(dhIntConfigVO.getApproaches()))
                .build();
    }

    /**
     * @param dhIntInfoVO
     * @return
     */
    private static IntersectionInfoVO convertIntInfo(DataHubIntersectionInfoVO dhIntInfoVO) {
        if (dhIntInfoVO == null) {
            throw new IllegalArgumentException();
        }

        DataHubLocationVO dhLocationVO = dhIntInfoVO.getLocation();
        LocationVO locationVO = LocationVO.builder()
                .latitude(dhLocationVO.getLatitude())
                .longitude(dhLocationVO.getLongitude())
                .build();
        return IntersectionInfoVO.builder()
                .name(dhIntInfoVO.getName())
                .model(dhIntInfoVO.getModel())
                .version(dhIntInfoVO.getVersion())
                .dateWritten(dhIntInfoVO.getDateWritten())
                .location(locationVO)
                .timezone(dhIntInfoVO.getTimezone())
                .build();
    }

    /**
     * @param dhPatternInfoVO
     * @return
     */
    private static PatternInfoVO convertPatternInfo(DataHubPatternInfoVO dhPatternInfoVO) {
        if (dhPatternInfoVO == null) {
            throw new IllegalArgumentException();
        }
        return PatternInfoVO.builder()
                .cycleLength(dhPatternInfoVO.getCycleLength())
                .offsetTime(dhPatternInfoVO.getOffsetTime())
                .sequenceNumber(dhPatternInfoVO.getSequenceNumber())
                .splitTime(dhPatternInfoVO.getSplitTime())
                .splitMode(dhPatternInfoVO.getSplitMode())
                .splitCoordPhase(dhPatternInfoVO.getSplitCoordPhase())
                .build();
    }

    /**
     * @param dhDetectorInfo
     * @return
     */
    private static DetectorInfoVO convertDetInfo(DataHubDetectorInfoVO dhDetectorInfo) {
        if (dhDetectorInfo == null) {
            throw new IllegalArgumentException();
        }

        return DetectorInfoVO.builder()
                .detCallPhases(dhDetectorInfo.getDetCallPhases())
                .detMode(dhDetectorInfo.getDetMode())
                .build();
    }

    /**
     * @param dhSequenceInfo
     * @return
     */
    private static SequenceInfoVO convertSequenceInfo(DataHubSequenceInfoVO dhSequenceInfo) {
        if (dhSequenceInfo == null) {
            throw new IllegalArgumentException();
        }

        return SequenceInfoVO.builder()
                .defaultSequence(dhSequenceInfo.getDefaultSequence())
                .build();
    }

    /**
     * @param dhRingStructureInfoVO
     * @return
     */
    private static RingStructureInfoVO convertRingStructureInfo(DataHubRingStructureInfoVO dhRingStructureInfoVO) {
        if (dhRingStructureInfoVO == null) {
            throw new IllegalArgumentException();
        }

        return RingStructureInfoVO.builder()
                .sequenceData(dhRingStructureInfoVO.getSequenceData())
                .phaseInitialState(dhRingStructureInfoVO.getPhaseInitialState())
                .build();
    }

    /**
     * @param intUUID
     * @param dhPerfLogChunkVO
     * @return
     */
    private static PerfLogChunkVO convertPerfLogChunkVO(String intUUID, DataHubPerfLogChunkVO dhPerfLogChunkVO) {
        if (intUUID == null || dhPerfLogChunkVO == null) {
            throw new IllegalArgumentException();
        }

        PerfLogChunkVO perfLogChunkVO = PerfLogChunkVO.builder()
                .fromTime(dhPerfLogChunkVO.getFromTime())
                .toTime(dhPerfLogChunkVO.getToTime())
                .build();
        perfLogChunkVO.setConfigID(dhPerfLogChunkVO.getConfigId());
        perfLogChunkVO.setPerfLogEvents(convertPerfLogEventVO(dhPerfLogChunkVO.getEvents()));
        return perfLogChunkVO;
    }

    /**
     * @param dhPerfLogEventVOList
     * @return List<PerfLogEventVO>
     */
    private static List<PerfLogEventVO> convertPerfLogEventVO(List<DataHubPerfLogEventVO> dhPerfLogEventVOList) {
        if (dhPerfLogEventVOList == null) {
            throw new IllegalArgumentException();
        }

        ArrayList<PerfLogEventVO> perfLogEventVOList = new ArrayList<>();
        for (DataHubPerfLogEventVO dhPerfLogEventVO : dhPerfLogEventVOList) {
            if (accept(dhPerfLogEventVO)) {
                perfLogEventVOList.add(convertPerfLogEventVO(dhPerfLogEventVO));
            }
        }

        return perfLogEventVOList;
    }

    /**
     * @param dhPerfLogEventVO
     * @return PerfLogEventVO
     */
    @SuppressWarnings("java:S1144")
    public static PerfLogEventVO convertPerfLogEventVO(DataHubPerfLogEventVO dhPerfLogEventVO) {
        if (dhPerfLogEventVO == null) {
            throw new IllegalArgumentException();
        }

        return PerfLogEventVO.builder()
                .dateTime(dhPerfLogEventVO.getDateTime())
                .event(PerfLogEventVO.getEventMap().get(dhPerfLogEventVO.getEvent()))
                .parameter(dhPerfLogEventVO.getParameter())
                .ordinal(dhPerfLogEventVO.getOrdinal())
                .build();
    }

    /**
     * Filter out unsupported PerfLog events
     *
     * @param dhPerfLogEventVO
     * @return
     */
    private static boolean accept(DataHubPerfLogEventVO dhPerfLogEventVO) {
        if (dhPerfLogEventVO == null) {
            return false;
        }
        PerfLogEventVO.Event event = PerfLogEventVO.getEventMap().get(dhPerfLogEventVO.getEvent());
        // Exceptions are configured here
        return event != null;
    }

}
