/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : DirectPerfLogRetriever.java
 * Project     : SPM Platform
 */
package com.siemens.spm.perflog.boundary;

import com.siemens.spm.datahub.exception.DataHubException;
import com.siemens.spm.perflog.vo.PerfLogBundleVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;

import java.time.LocalDateTime;
import java.util.List;

public interface DirectPerfLogRetriever extends PerfLogRetriever {

    PerfLogBundleVO getPerfLogAllowNoData(Integer agencyId,
                                          String intUUID,
                                          LocalDateTime fromTime,
                                          LocalDateTime toTime);

    PerfLogEventVO getLastCoordPatternChanged(Integer agencyId,
                                              String intUUID,
                                              LocalDateTime fromTime,
                                              LocalDateTime toTime) throws DataHubException;

    PerfLogBundleVO getPerfLog(Integer agencyId,
                               String intUUID,
                               LocalDateTime fromTime,
                               LocalDateTime toTime,
                               List<Integer> eventTypes);
}
