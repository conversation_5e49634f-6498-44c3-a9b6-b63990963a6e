/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AutoPerfLogRetrieverStrategy.java
 * Project     : SPM Platform
 */
package com.siemens.spm.perflog.strategy;

import com.siemens.spm.common.constant.SProfile;
import com.siemens.spm.perflog.boundary.AutoPerfLogRetriever;
import com.siemens.spm.perflog.boundary.CachedPerfLogRetriever;
import com.siemens.spm.perflog.boundary.DirectPerfLogRetriever;
import com.siemens.spm.perflog.exception.MissedCacheException;
import com.siemens.spm.perflog.vo.PerfLogBundleVO;
import com.siemens.spm.perflog.vo.PerfLogChunkVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;
import com.siemens.spm.perflog.vo.PerfLogGapVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;

import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.util.List;

@Slf4j
public class AutoPerfLogRetrieverBean implements AutoPerfLogRetriever {

    @Autowired
    @Qualifier("cachedPerfLogRetriever")
    private CachedPerfLogRetriever cachedPerfLogRetriever;

    @Autowired
    @Qualifier("directPerfLogRetriever")
    private DirectPerfLogRetriever directPerfLogRetriever;

    @Value("${spring.profiles.active:}")
    private String activeProfiles;

    @Override
    public PerfLogBundleVO getPerfLog(Integer agencyId, String intUUID, LocalDateTime fromTime, LocalDateTime toTime) {
        if (agencyId == null || intUUID == null || fromTime == null || toTime == null) {
            throw new IllegalArgumentException();
        }

        return getPerfLogFromDataHub(agencyId, intUUID, fromTime, toTime);

        // FIXME: The current cache mechanism is very poor performance,
        //  so we temporarily disable it and always get PerfLog from Data Hub directly.
        //  This should be resolved in the future.

        //        log.info("Retrieving PerfLog from cache");
        //        PerfLogBundleVO perfLogBundleVO = getPerfLogFromCache(agencyUUID, intUUID, fromTime, toTime);
        //
        //        if (!validateCacheData(perfLogBundleVO)) {
        //            perfLogBundleVO = getPerfLogFromDataHub(agencyUUID, intUUID, fromTime, toTime);
        //        }
        //
        //        return perfLogBundleVO;
    }

    @Override
    public PerfLogBundleVO getPerfLogAllowNoData(Integer agencyId,
                                                 String intUUID,
                                                 LocalDateTime fromTime,
                                                 LocalDateTime toTime) {
        if (agencyId == null || intUUID == null || fromTime == null || toTime == null) {
            throw new IllegalArgumentException();
        }

        PerfLogBundleVO perfLogBundleVO = null;

        perfLogBundleVO = directPerfLogRetriever.getPerfLogAllowNoData(agencyId, intUUID, fromTime, toTime);
        log.info("Retrieved PerfLog from Data Hub with agencyId {} intUUID {} fromTime {} toTime {}", agencyId, intUUID,
                fromTime, toTime);

        return perfLogBundleVO;
    }

    /**
     * @return
     */
    private PerfLogBundleVO getPerfLogFromCache(Integer agencyId,
                                                String intUUID,
                                                LocalDateTime fromTime,
                                                LocalDateTime toTime) {
        PerfLogBundleVO perfLogBundleVO = null;

        try {
            perfLogBundleVO = cachedPerfLogRetriever.getPerfLog(agencyId, intUUID, fromTime, toTime);
        } catch (MissedCacheException e) {
            String msg = MessageFormat.format("Missed cache: agencyUUID={0}, intUUID={1}, fromTime={2}, toTime={3}",
                    agencyId, intUUID, fromTime, toTime);
            log.info(msg);
        }

        return perfLogBundleVO;
    }

    /**
     * Validate if any chunk contains event NO_DATA or not?
     *
     * @param perfLogBundleVO
     * @return true if no event NO_DATA was found
     */
    private boolean validateCacheData(PerfLogBundleVO perfLogBundleVO) {
        if (perfLogBundleVO == null ||
                perfLogBundleVO.getPerfLogChunks() == null ||
                perfLogBundleVO.getPerfLogChunks().isEmpty()) {
            log.info("Retrieving PerfLog from Data Hub");
            return false;
        }

        for (PerfLogChunkVO chunk : perfLogBundleVO.getPerfLogChunks()) {
            List<PerfLogEventVO> perfLogEvents = chunk.getPerfLogEvents();
            if (perfLogEvents == null ||
                    perfLogEvents.isEmpty() ||
                    PerfLogEventVO.Event.NO_DATA.equals(perfLogEvents.get(0).getEvent())) {
                return false;
            }
        }

        return true;
    }

    /**
     * @return
     */
    private PerfLogBundleVO getPerfLogFromDataHub(Integer agencyId,
                                                  String intUUID,
                                                  LocalDateTime fromTime,
                                                  LocalDateTime toTime) {
        PerfLogBundleVO perfLogBundleVO = null;

        try {
            log.info("Retrieving PerfLog from Data Hub for intersection {} of agency {} from: {} to: {}",
                    intUUID, 
                    agencyId,
                    fromTime,
                    toTime);
            perfLogBundleVO = directPerfLogRetriever.getPerfLog(agencyId, intUUID, fromTime, toTime);
            log.info("Retrieved PerfLog from Data Hub");
        } catch (MissedCacheException e) {
            // Should not happen
            log.error("DirectPerfLogRetriever should not throw MissedCacheException", e);
        }

        return perfLogBundleVO;
    }

    /**
     * Always get PerfLog gaps from Data Hub
     */
    @Override
    public List<PerfLogGapVO> getPerfLogGap(Integer agencyId,
                                            String intUUID,
                                            LocalDateTime fromTime,
                                            LocalDateTime toTime) {
        log.debug("Retrieving PerfLog gaps");

        List<PerfLogGapVO> perfLogGapVOList;
        if (activeProfiles != null && activeProfiles.contains(SProfile.DEV)) {
            // Only get gaps from cache in dev profile for testing, where Data Hub is not available.
            // Otherwise, should get gaps from Data Hub because missing data may be re-uploaded.
            perfLogGapVOList = cachedPerfLogRetriever.getPerfLogGap(agencyId, intUUID, fromTime, toTime);
        } else {
            perfLogGapVOList = directPerfLogRetriever.getPerfLogGap(agencyId, intUUID, fromTime, toTime);
        }

        return perfLogGapVOList;
    }

}
