/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : PerfLogChunkRepository.java
 * Project     : SPM Platform
 */

package com.siemens.spm.perflog.repository;

import com.siemens.spm.perflog.persistence.PerfLogChunk;
import com.siemens.spm.perflog.persistence.PerfLogChunkID;
import com.siemens.spm.perflog.persistence.PerfLogChunkView;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface PerfLogChunkViewRepositoryBase extends JpaRepository<PerfLogChunk, PerfLogChunkID> {

    List<PerfLogChunkView> findByIntUUIDAndFromTimeGreaterThanEqualAndToTimeLessThanEqual(String intUUID,
        String fromTime, String toTime);

}
