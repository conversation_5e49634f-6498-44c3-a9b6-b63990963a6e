/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AutoPerfLogRetriever.java
 * Project     : SPM Platform
 */
package com.siemens.spm.perflog.boundary;

import com.siemens.spm.perflog.vo.PerfLogBundleVO;
import com.siemens.spm.perflog.vo.PerfLogGapVO;

import java.time.LocalDateTime;
import java.util.List;

public interface AutoPerfLogRetriever {

    /**
     * Returned PerfLog chunks may be expanded from requested fromTime and toTime
     *
     * @param agencyId
     * @param intUUID
     * @param fromTime
     * @param toTime
     * @return
     */
    PerfLogBundleVO getPerfLog(Integer agencyId,
                               String intUUID,
                               LocalDateTime fromTime,
                               LocalDateTime toTime);

    PerfLogBundleVO getPerfLogAllowNoData(Integer agencyId,
                                          String intUUID,
                                          LocalDateTime fromTime,
                                          LocalDateTime toTime);

    List<PerfLogGapVO> getPerfLogGap(Integer agencyId,
                                     String intUUID,
                                     LocalDateTime fromTime,
                                     LocalDateTime toTime);

}
