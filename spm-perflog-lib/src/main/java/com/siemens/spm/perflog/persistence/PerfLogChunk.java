/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : PerfLogChunk.java
 * Project     : SPM Platform
 */
package com.siemens.spm.perflog.persistence;

import com.siemens.spm.common.constant.AgencyConstants;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.IdClass;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinColumns;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;

import com.siemens.spm.perflog.domaintype.PerfLogChunkStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "perflog_chunk", schema = AgencyConstants.AGENCY_SCHEMA_PLACEHOLDER)
@IdClass(PerfLogChunkID.class)
public class PerfLogChunk {

    @Id
    @Column(name = "int_uuid", length = 40, nullable = false)
    private String intUUID;

    @Id
    @Column(name = "from_time")
    private String fromTime;

    @Id
    @Column(name = "to_time")
    private String toTime;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumns({
            @JoinColumn(name = "config_uuid", referencedColumnName = "uuid", updatable = false),
            @JoinColumn(name = "config_from_time_utc", referencedColumnName = "from_time_utc", updatable = false)
    })
    private IntersectionConfig intConfig;

    /**
     * JSON of List<PerfLogEventVO>
     */
    @Column(name = "event_lob", columnDefinition = "TEXT")
    private String eventLob;

    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private PerfLogChunkStatus status;

    /**
     * Check perflog chunk no data or not?
     *
     * @return
     */
    public boolean isNoData() {
        return PerfLogChunkStatus.NO_DATA.equals(status);
    }

}
