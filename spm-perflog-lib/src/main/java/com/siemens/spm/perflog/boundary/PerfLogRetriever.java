/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : PerfLogRetriever.java
 * Project     : SPM Platform
 */
package com.siemens.spm.perflog.boundary;

import com.siemens.spm.perflog.exception.MissedCacheException;
import com.siemens.spm.perflog.vo.PerfLogBundleVO;
import com.siemens.spm.perflog.vo.PerfLogGapVO;

import java.time.LocalDateTime;
import java.util.List;

public interface PerfLogRetriever {

    PerfLogBundleVO getPerfLog(Integer agencyId,
                               String intUUID,
                               LocalDateTime fromTime,
                               LocalDateTime toTime)
            throws MissedCacheException;

    List<PerfLogGapVO> getPerfLogGap(Integer agencyId,
                                     String intUUID,
                                     LocalDateTime fromTime,
                                     LocalDateTime toTime);

}
