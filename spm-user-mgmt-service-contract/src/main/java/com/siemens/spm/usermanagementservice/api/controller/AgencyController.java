// /*
//  * Copyright (C) Siemens.  All Rights Reserved.
//  *
//  * Source      : AgencyController.java
//  * Project     : SPM Platform
//  */
// package com.siemens.spm.usermanagementservice.api.controller;

// import jakarta.validation.Valid;
// import jakarta.validation.constraints.NotNull;

// import org.springframework.http.HttpStatus;
// import org.springframework.http.ResponseEntity;
// import org.springframework.validation.annotation.Validated;
// import org.springframework.web.bind.annotation.GetMapping;
// import org.springframework.web.bind.annotation.PathVariable;
// import org.springframework.web.bind.annotation.PutMapping;
// import org.springframework.web.bind.annotation.RequestBody;
// import org.springframework.web.bind.annotation.RequestMapping;
// import org.springframework.web.bind.annotation.ResponseStatus;

// import com.siemens.spm.common.api.PublicController;
// import com.siemens.spm.usermanagementservice.api.vo.request.AgencyLicenseUpdateRequestVO;
// import com.siemens.spm.usermanagementservice.api.vo.request.SetAgencySettingsRequestVO;
// import com.siemens.spm.usermanagementservice.api.vo.response.AgencyDetailResultObject;
// import com.siemens.spm.usermanagementservice.api.vo.response.AgencyLicenseResultObject;
// import com.siemens.spm.usermanagementservice.api.vo.response.AgencySettingsResultObject;
// import io.swagger.v3.oas.annotations.Operation;
// import io.swagger.v3.oas.annotations.Parameter;
// import io.swagger.v3.oas.annotations.enums.ParameterIn;
// import io.swagger.v3.oas.annotations.responses.ApiResponse;
// import io.swagger.v3.oas.annotations.responses.ApiResponses;
// import io.swagger.v3.oas.annotations.tags.Tag;

// /**
//  * <AUTHOR> Nguyen
//  */
// @RequestMapping(AgencyController.URL_ROOT)
// @Tag(name = "agency", description = "Agency API")
// @Validated
// public interface AgencyController extends PublicController {

//     String VERSION = "/v1";
//     String AGENCIES_RESOURCE = "/agencies";
//     String URL_ROOT = PUBLIC_API + VERSION + AGENCIES_RESOURCE;

//     /**
//      * Get agency detail by Id
//      *
//      * @phase agencyId
//      * @return AgencyDetailResultObject
//      */
//     @Operation(summary = "Get agency detail by Id")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "400", description = "Bad Request"),
//             @ApiResponse(responseCode = "404", description = "Not Found")
//     })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}",
//             required = true, in = ParameterIn.HEADER, description = "Bearer Token type")

//     @ResponseStatus(HttpStatus.OK)
//     @GetMapping("{agency_id}")
//     ResponseEntity<AgencyDetailResultObject> getAgencyDetail(
//             @Parameter(name = "agency_id", description = "Agency Id")
//             @PathVariable("agency_id")
//             @NotNull(message = "agency_id_not_null") final Integer agencyId
//     );

//     // TODO Update API doc

//     /**
//      * GET /agencies/{agency_id}/settings
//      */
//     @GetMapping("/{agency_id}/settings")
//     @ResponseStatus(HttpStatus.OK)
//     ResponseEntity<AgencySettingsResultObject> getAgencySettings(
//             @Parameter(name = "agency_id", description = "Agency Id")
//             @PathVariable("agency_id")
//             @NotNull(message = "agency_id_not_null") final Integer agencyId
//     );

//     // TODO Update API doc

//     /**
//      * PUT /agencies/{agency_id}/settings
//      */
//     @PutMapping("/{agency_id}/settings")
//     @ResponseStatus(HttpStatus.NO_CONTENT)
//     ResponseEntity<AgencySettingsResultObject> setAgencySettings(
//             @Parameter(name = "agency_id", description = "Agency id")
//             @PathVariable("agency_id")
//             @NotNull(message = "agency_id_not_null") final Integer agencyId,

//             @Valid @NotNull(message = "agency_settings_must_not_be_null")
//             @RequestBody
//             SetAgencySettingsRequestVO requestVO
//     );

//     @GetMapping("/{agency_id}/licenses")
//     @ResponseStatus(HttpStatus.OK)
//     ResponseEntity<AgencyLicenseResultObject> getAgencyLicenses(
//             @Parameter(name = "agency_id", description = "Agency id")
//             @PathVariable("agency_id")
//             @NotNull(message = "agency_id_not_null") final Integer agencyId
//     );

//     @PutMapping("/{agency_id}/licenses")
//     @ResponseStatus(HttpStatus.OK)
//     ResponseEntity<AgencyLicenseResultObject> setAgencyLicenses(
//             @Parameter(name = "agency_id", description = "Agency id")
//             @PathVariable("agency_id")
//             @NotNull(message = "agency_id_not_null") final Integer agencyId,

//             @Valid @NotNull(message = "agency_settings_must_not_be_null")
//             @RequestBody
//             AgencyLicenseUpdateRequestVO requestVO
//     );

// }
