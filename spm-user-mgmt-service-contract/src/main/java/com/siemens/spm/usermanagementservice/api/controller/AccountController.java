// /*
//  * Copyright (C) Siemens.  All Rights Reserved.
//  *
//  * Source      : AcountResource.java
//  * Project     : SPM Platform
//  */
// package com.siemens.spm.usermanagementservice.api.controller;

// import jakarta.validation.Valid;

// import org.springframework.http.HttpStatus;
// import org.springframework.http.ResponseEntity;
// import org.springframework.web.bind.annotation.GetMapping;
// import org.springframework.web.bind.annotation.PatchMapping;
// import org.springframework.web.bind.annotation.PostMapping;
// import org.springframework.web.bind.annotation.RequestBody;
// import org.springframework.web.bind.annotation.RequestMapping;
// import org.springframework.web.bind.annotation.RequestParam;
// import org.springframework.web.bind.annotation.ResponseStatus;

// import com.siemens.spm.common.api.PublicController;
// import com.siemens.spm.usermanagementservice.api.vo.request.LanguageRequestVO;
// import com.siemens.spm.usermanagementservice.api.vo.request.UserKeyRequestVO;
// import com.siemens.spm.usermanagementservice.api.vo.response.AgencyUserAccessResultObject;
// import com.siemens.spm.usermanagementservice.api.vo.response.ChangeLanguageResultObject;
// import com.siemens.spm.usermanagementservice.api.vo.response.UserKeyResultObject;
// import com.siemens.spm.usermanagementservice.api.vo.response.UserResultObject;
// import io.swagger.v3.oas.annotations.Operation;
// import io.swagger.v3.oas.annotations.Parameter;
// import io.swagger.v3.oas.annotations.enums.ParameterIn;
// import io.swagger.v3.oas.annotations.responses.ApiResponse;
// import io.swagger.v3.oas.annotations.responses.ApiResponses;
// import io.swagger.v3.oas.annotations.tags.Tag;

// /**
//  * Controller for account API
//  *
//  * <AUTHOR> Nguyen
//  */
// @RequestMapping(AccountController.URL_ROOT)
// @Tag(name = "account", description = "Account API")
// public interface AccountController extends PublicController {

//     String VERSION = "/v1";
//     String ACCOUNT_RESOURCE = "/account";
//     String URL_ROOT = PUBLIC_API + VERSION + ACCOUNT_RESOURCE;

//     @Operation(summary = "Access specific agency or default agency of current user")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "400", description = "Bad Request"),
//             @ApiResponse(responseCode = "404", description = "Not Found")
//     })
//     @ResponseStatus(HttpStatus.OK)
//     @GetMapping("agency-accession")
//     ResponseEntity<AgencyUserAccessResultObject> accessAgency(
//             @RequestParam(name = "agency_id", required = false) Integer agencyId
//     );

//     /**
//      * GET /account : Get the current user.
//      *
//      * @return the current user
//      */
//     @Operation(summary = "Get the current user")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "400", description = "Bad Request"),
//             @ApiResponse(responseCode = "404", description = "Not Found")
//     })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
//             description = "Bearer Token type")

//     @ResponseStatus(HttpStatus.OK)
//     @GetMapping
//     ResponseEntity<UserResultObject> getAccount();

//     /**
//      * POST /account/user-key : Get user key
//      *
//      * @return the generated user key
//      */
//     @Operation(summary = "Get current user key")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "201", description = "Created"),
//             @ApiResponse(responseCode = "400", description = "Bad Request"),
//             @ApiResponse(responseCode = "404", description = "Not Found")
//     })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
//             description = "Bearer Token type")

//     @ResponseStatus(HttpStatus.CREATED)
//     @PostMapping("user-key")
//     ResponseEntity<UserKeyResultObject> getUserKey(
//             @Parameter(name = "request_body", description = "Current password")
//             @Valid @RequestBody
//             UserKeyRequestVO currentPasswordVO);

//     /**
//      * POST /account/new-user-key : Create user key
//      *
//      * @return the generated user key
//      */
//     @Operation(summary = "Create new user key")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "201", description = "Created"),
//             @ApiResponse(responseCode = "400", description = "Bad Request"),
//             @ApiResponse(responseCode = "404", description = "Not Found")
//     })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
//             description = "Bearer Token type")

//     @ResponseStatus(HttpStatus.CREATED)
//     @PostMapping("new-user-key")
//     ResponseEntity<UserKeyResultObject> createUserKey(
//             @Parameter(name = "request_body", description = "Current password")
//             @Valid @RequestBody
//             UserKeyRequestVO currentPasswordVO);

//     /**
//      * PATCH /account/language Changes the current user's language
//      *
//      * @phase langRequestVO: contains a language will change to
//      * @return ChangeLanguageResultObject
//      */
//     @Operation(summary = "Change the current user's language")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "204", description = "No Content"),
//             @ApiResponse(responseCode = "400", description = "Bad Request"),
//             @ApiResponse(responseCode = "404", description = "Not Found")
//     })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
//             description = "Bearer Token type")
//     @ResponseStatus(HttpStatus.NO_CONTENT)
//     @PatchMapping("language")
//     ResponseEntity<ChangeLanguageResultObject> updateLanguage(
//             @Parameter(name = "request_body", description = "Language change request")
//             @RequestBody LanguageRequestVO langRequestVO);

// }
