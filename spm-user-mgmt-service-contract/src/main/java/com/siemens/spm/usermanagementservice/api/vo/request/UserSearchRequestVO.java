/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : IntersectionSearchRequestVO.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.api.vo.request;

import java.io.Serializable;
import java.util.List;

import jakarta.validation.constraints.Pattern;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.common.shared.resource.UUIDConstants;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class UserSearchRequestVO implements Serializable {

    private static final long serialVersionUID = 1871727637122786102L;

    @JsonProperty("agency_id")
    private Integer agencyId;

    @JsonProperty("exclusionary_ids")
    private List<Long> exclusionaryIds;

    @JsonProperty("order_by_columns")
    private String[] orderByColumns;

    @JsonProperty("page")
    private Integer page;

    @JsonProperty("size")
    private Integer size;

    @JsonProperty("status")
    private String status;

    @JsonProperty("text")
    private String text;

    @JsonProperty("user_ids")
    private List<Long> userIds;

    @JsonProperty("roles")
    private List<String> roles;

}
