/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : NotificationInterComController.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.api.intercom;

import com.siemens.spm.common.intercom.InterComController;
import com.siemens.spm.common.util.RestUtils;
import com.siemens.spm.usermanagementservice.api.vo.ReleaseNoteVO;
import com.siemens.spm.usermanagementservice.api.vo.request.NotificationsCreateRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.response.NotificationsCreateResultObject;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * Internal REST interface providing information to other services. This interface is implemented by the actual running
 * service.
 *
 * <AUTHOR>
 */
@RequestMapping(NotificationInterComController.API_ROOT)
public interface NotificationInterComController extends InterComController {

    String API_ROOT = INTERNAL_API + "/v1";

    String NOTIFICATIONS_RESOURCE = "/notifications";

    String RELEASE_NOTES_RESOURCE = NOTIFICATIONS_RESOURCE + "/release-notes";

    /**
     * Create notifications for users
     *
     * @param requestVO
     * @return ResponseEntity<NotificationsCreateResultObject>
     */
    @PostMapping(NOTIFICATIONS_RESOURCE)
    ResponseEntity<NotificationsCreateResultObject> createNotificationsForUsers(@Valid @RequestBody NotificationsCreateRequestVO requestVO);

    /**
     * invoke invokeCreateNotificationsForUsers request
     *
     * @param endpoint  endpoint of target service
     * @param requestVO NotificationsCreateRequestVO
     * @return ResponseEntity<NotificationsCreateResultObject>
     */
    static ResponseEntity<NotificationsCreateResultObject> invokeCreateNotificationsForUsers(String endpoint,
                                                                                                    NotificationsCreateRequestVO requestVO) {
        String url = endpoint + API_ROOT + NOTIFICATIONS_RESOURCE;
        Integer agencyId = requestVO.getAgencyId();
        return RestUtils.postWithAgencyHeader(url, agencyId.toString(), requestVO, NotificationsCreateResultObject.class);
    }

    /**
     * Create release note notifications to all users
     *
     * @return ResponseEntity<NotificationsCreateResultObject>
     */
    @PostMapping(RELEASE_NOTES_RESOURCE)
    ResponseEntity<NotificationsCreateResultObject> createReleaseNoteNotifications(@RequestBody(required = false) ReleaseNoteVO releaseNoteVO);

}
