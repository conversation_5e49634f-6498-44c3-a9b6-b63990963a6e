/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : IntersectionsInAgencyVerifyRequestVO.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.api.vo.request;

import java.io.Serializable;
import java.util.Collection;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(Include.NON_NULL)
public class IntersectionsInAgencyVerifyRequestVO implements Serializable {

    private static final long serialVersionUID = -8441833311001297371L;

    @JsonProperty("agency_id")
    private Integer agencyId;

    @JsonProperty("intersection_ids")
    private Collection<String> intersectionIds;

}
