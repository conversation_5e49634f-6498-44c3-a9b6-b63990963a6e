// package com.siemens.spm.usermanagementservice.api.controller;

// import jakarta.validation.Valid;

// import org.springframework.http.HttpStatus;
// import org.springframework.http.ResponseEntity;
// import org.springframework.web.bind.annotation.GetMapping;
// import org.springframework.web.bind.annotation.PatchMapping;
// import org.springframework.web.bind.annotation.PathVariable;
// import org.springframework.web.bind.annotation.PutMapping;
// import org.springframework.web.bind.annotation.RequestBody;
// import org.springframework.web.bind.annotation.RequestMapping;
// import org.springframework.web.bind.annotation.ResponseStatus;

// import com.siemens.spm.common.api.PublicController;
// import com.siemens.spm.usermanagementservice.api.vo.request.UpdateNotiSettingRequestVO;
// import com.siemens.spm.usermanagementservice.api.vo.request.UpdateWidgetsSettingRequestVO;
// import com.siemens.spm.usermanagementservice.api.vo.response.AgencyUserSettingMetadataResultObject;
// import com.siemens.spm.usermanagementservice.api.vo.response.AgencyUserSettingResultObject;
// import com.siemens.spm.usermanagementservice.api.vo.response.NotiSettingResultObject;
// import com.siemens.spm.usermanagementservice.api.vo.response.NotiSettingUpdatedResultObject;
// import com.siemens.spm.usermanagementservice.api.vo.response.WidgetsSettingUpdatedResultObject;
// import io.swagger.v3.oas.annotations.Operation;
// import io.swagger.v3.oas.annotations.Parameter;
// import io.swagger.v3.oas.annotations.enums.ParameterIn;
// import io.swagger.v3.oas.annotations.responses.ApiResponse;
// import io.swagger.v3.oas.annotations.responses.ApiResponses;
// import io.swagger.v3.oas.annotations.tags.Tag;

// /**
//  * Controller for account API on specific agency
//  *
//  * <AUTHOR> Nguyen - <EMAIL>
//  */
// @RequestMapping(AccountAgencyController.URL_ROOT)
// @Tag(name = "agency-account", description = "Account API on specific agency")
// public interface AccountAgencyController extends PublicController {

//     String VERSION = "/v1";
//     String URL_ROOT = PUBLIC_API + VERSION + "/agencies/{agency_id}/account";

//     String SETTINGS_RESOURCE = "/settings";

//     String DASHBOARD_SETTING_RESOURCE = SETTINGS_RESOURCE + "/dashboard";

//     String WIDGETS_SETTING_RESOURCE = DASHBOARD_SETTING_RESOURCE + "/widgets";
//     String SETTING_METADATA_RESOURCE = SETTINGS_RESOURCE + "/metadata";
//     String NOTI_SETTING_RESOURCE = SETTINGS_RESOURCE + "/notifications";

//     /**
//      * GET: /account/settings : Get setting data of current user depend on agency
//      *
//      * @return {@link  AgencyUserSettingResultObject}
//      */
//     @Operation(summary = "Get setting data of current user")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "200", description = "Success"),
//             @ApiResponse(responseCode = "404", description = "Not Found"),
//             @ApiResponse(responseCode = "500", description = "Internal server error")
//     })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
//             description = "Bearer Token type")
//     @GetMapping(SETTINGS_RESOURCE)
//     ResponseEntity<AgencyUserSettingResultObject> getUserSetting(
//             @Parameter(name = "agency_id")
//             @PathVariable(name = "agency_id") Integer agencyId
//     );

//     @Operation(summary = "Get notification setting data of current user on specific agency")
//     @GetMapping(NOTI_SETTING_RESOURCE)
//     ResponseEntity<NotiSettingResultObject> getNotiSetting(
//             @Parameter(name = "agency_id")
//             @PathVariable(name = "agency_id") Integer agencyId
//     );

//     /**
//      * GET: /account/settings/metadata
//      *
//      * @phase agencyId id of current agencyId. Only need for user who has permission on all agencies.
//      * @return {@link  AgencyUserSettingMetadataResultObject}
//      */
//     @Operation(summary = "Get setting's metadata for specific agency")
//     @ApiResponses(
//             @ApiResponse(responseCode = "200", description = "Success")
//     )
//     @GetMapping(SETTING_METADATA_RESOURCE)
//     @ResponseStatus(HttpStatus.OK)
//     ResponseEntity<AgencyUserSettingMetadataResultObject> getUserSettingMetadata(
//             @Parameter(name = "agency_id")
//             @PathVariable(name = "agency_id") Integer agencyId
//     );

//     /**
//      * PUT: /account/settings/dashboard/widgets : Update all widget's setting of current user on specific agency
//      *
//      * @phase requestVO {@link UpdateWidgetsSettingRequestVO}
//      * @return {@link WidgetsSettingUpdatedResultObject}
//      */
//     @Operation(summary = "Update all widget's setting of current user on specific agency")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "200", description = "Success"),
//             @ApiResponse(responseCode = "400", description = "Bad Request"),
//             @ApiResponse(responseCode = "404", description = "Not Found"),
//             @ApiResponse(responseCode = "500", description = "Internal server error")
//     })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
//             description = "Bearer Token type")
//     @PutMapping(WIDGETS_SETTING_RESOURCE)
//     ResponseEntity<WidgetsSettingUpdatedResultObject> updateAllWidgetsSetting(
//             @Parameter(name = "agency_id")
//             @PathVariable(name = "agency_id") Integer agencyId,

//             @Parameter(name = "request_body", example = "widget's setting to update")
//             @Valid @RequestBody UpdateWidgetsSettingRequestVO requestVO);

//     /**
//      * PATCH: /account/settings/dashboard/widgets : Update a specific widget's setting of current user on specific
//      * agency
//      *
//      * @phase requestVO {@link UpdateWidgetsSettingRequestVO}
//      * @return {@link WidgetsSettingUpdatedResultObject}
//      */
//     @Operation(summary = "Update a specific widget's setting of current user on specific agency")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "200", description = "Success"),
//             @ApiResponse(responseCode = "400", description = "Bad Request"),
//             @ApiResponse(responseCode = "404", description = "Not Found"),
//             @ApiResponse(responseCode = "500", description = "Internal server error")
//     })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
//             description = "Bearer Token type")
//     @PatchMapping(WIDGETS_SETTING_RESOURCE)
//     ResponseEntity<WidgetsSettingUpdatedResultObject> updateSpecificWidgetSetting(
//             @Parameter(name = "agency_id")
//             @PathVariable(name = "agency_id") Integer agencyId,

//             @Parameter(name = "request_body", description = "widget's setting to update")
//             @Valid @RequestBody UpdateWidgetsSettingRequestVO requestVO);

//     @PutMapping(NOTI_SETTING_RESOURCE)
//     ResponseEntity<NotiSettingUpdatedResultObject> updateNotiSetting(
//             @Parameter(name = "agency_id")
//             @PathVariable(name = "agency_id") Integer agencyId,

//             @Valid @RequestBody UpdateNotiSettingRequestVO requestVO
//     );

// }
