# Input Validation Test Cases CSV
# This file provides test cases for input validation scenarios
# Format: testName,eventsCount,hasNullEvents,hasNullDateTime,hasNullEndTime,expectException,expectedExceptionType
testName,eventsCount,hasNullEvents,hasNullDateTime,hasNullEndTime,expectException,expectedExceptionType
valid_input_small,5,false,false,false,false,NONE
valid_input_large,100,false,false,false,false,NONE
empty_events_list,0,false,false,false,false,NONE
null_events_list,0,true,false,false,true,NullPointerException
null_end_time,5,false,false,true,true,NullPointerException
events_with_null_datetime,5,false,true,false,true,NullPointerException
single_event,1,false,false,false,false,NONE
maximum_events,1000,false,false,false,false,NONE
events_with_future_datetime,5,false,false,false,false,NONE
events_with_past_datetime,5,false,false,false,false,NONE
mixed_valid_invalid_events,10,false,true,false,true,NullPointerException
