# Data Validation Test Cases CSV
# This file provides test cases for validating loaded scenario data
# Format: scenario,expectSortedByTime,expectPhase1Events,expectPhase2Events,expectedUniquePhases
scenario,expectSortedByTime,expectPhase1Events,expectPhase2Events,expectedUniquePhases
complete_single_cycle,true,true,true,2
multiple_cycles_phase1,true,true,false,1
multi_phase_simultaneous,true,true,true,4
incomplete_missing_red_end,true,true,true,2
incomplete_missing_yellow,true,true,false,1
out_of_order_events,false,true,true,2
boundary_same_time,false,true,true,2
short_cycle_times,true,true,true,2
long_cycle_times,true,true,false,1
complex_multi_phase,true,true,true,8
invalid_mixed_events,false,true,true,3
cycles_missing_phases,true,true,true,2
overlapping_phases,true,true,true,3
duplicate_events,true,true,true,2
zero_duration_phases,true,true,false,1
extremely_long_phases,true,true,false,1
microsecond_precision,true,true,true,2
malformed_event_sequence,false,true,false,2
missing_phase_starts,false,true,true,2
orphaned_phase_ends,false,true,true,2
rapid_phase_changes,true,true,true,4
concurrent_multi_phase,true,true,true,6
boundary_midnight_crossing,true,true,false,1
