# Basic Scenarios Test Cases CSV
# This file provides test cases for CSV data loading functionality
# Format: scenario,shouldExist,expectedMinEvents,expectedMaxEvents
scenario,shouldExist,expectedMinEvents,expectedMaxEvents
complete_single_cycle,true,4,10
multiple_cycles_phase1,true,8,20
multi_phase_simultaneous,true,10,25
incomplete_missing_red_end,true,3,8
incomplete_missing_yellow,true,3,8
out_of_order_events,true,5,15
boundary_same_time,true,3,10
short_cycle_times,true,5,20
long_cycle_times,true,4,12
complex_multi_phase,true,15,50
invalid_mixed_events,true,5,15
cycles_missing_phases,true,3,12
overlapping_phases,true,8,25
duplicate_events,true,6,20
zero_duration_phases,true,4,15
extremely_long_phases,true,3,10
microsecond_precision,true,5,15
malformed_event_sequence,true,3,12
missing_phase_starts,true,2,10
orphaned_phase_ends,true,3,12
rapid_phase_changes,true,10,30
concurrent_multi_phase,true,15,40
boundary_midnight_crossing,true,5,15
# Non-existent scenarios for negative testing
non_existent_scenario_1,false,0,0
invalid_scenario_name,false,0,0
empty_scenario,false,0,0
