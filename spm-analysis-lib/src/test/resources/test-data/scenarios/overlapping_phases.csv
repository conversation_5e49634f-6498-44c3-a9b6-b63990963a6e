# Overlapping Phases Scenario
# Phases that overlap in time
datetime,event,param,ordinal_number
2025-02-14 06:25:00.000 +0700,1,1,107
2025-02-14 06:25:10.000 +0700,1,2,108
2025-02-14 06:25:20.000 +0700,1,3,109
2025-02-14 06:25:30.000 +0700,7,1,110
2025-02-14 06:25:32.000 +0700,7,2,111
2025-02-14 06:25:34.000 +0700,8,1,112
2025-02-14 06:25:35.000 +0700,7,3,113
2025-02-14 06:25:36.000 +0700,8,2,114
2025-02-14 06:25:38.000 +0700,9,1,115
2025-02-14 06:25:40.000 +0700,8,3,116
2025-02-14 06:25:42.000 +0700,9,2,117
2025-02-14 06:25:44.000 +0700,10,1,118
2025-02-14 06:25:46.000 +0700,9,3,119
2025-02-14 06:25:48.000 +0700,10,2,120
2025-02-14 06:25:50.000 +0700,10,3,121
