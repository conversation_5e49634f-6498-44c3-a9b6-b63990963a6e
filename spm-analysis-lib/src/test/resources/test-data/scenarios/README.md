# Test Data Scenarios

This directory contains separated test scenarios from the original `cycles-factory-test-data.csv` file. Each scenario tests different aspects of cycle detection and traffic signal analysis.

## Scenario Files

### Basic Cycle Tests
1. **complete_single_cycle.csv** (6 events, ordinals 0-5)
   - A complete G→Y→R cycle for phase 1
   - Tests normal cycle progression

2. **multiple_cycles_phase1.csv** (12 events, ordinals 6-17)
   - Multiple complete cycles for phase 1
   - Tests cycle repetition and consistency

3. **multi_phase_simultaneous.csv** (12 events, ordinals 18-29)
   - Multiple phases running simultaneously
   - Tests concurrent phase management

### Incomplete Cycle Tests
4. **incomplete_missing_red_end.csv** (5 events, ordinals 30-34)
   - Cycle missing red end event
   - Tests handling of incomplete cycles

5. **incomplete_missing_yellow.csv** (4 events, ordinals 35-38)
   - Cycle missing yellow phase
   - Tests yellow phase omission handling

### Timing and Order Tests
6. **out_of_order_events.csv** (6 events, ordinals 39-44)
   - Events not in chronological order
   - Tests temporal sequence validation

7. **boundary_same_time.csv** (6 events, ordinals 45-50)
   - Multiple events at same timestamp
   - Tests simultaneous event handling

8. **short_cycle_times.csv** (6 events, ordinals 51-56)
   - Very short cycle durations
   - Tests minimum timing constraints

9. **long_cycle_times.csv** (6 events, ordinals 57-62)
   - Extended cycle durations
   - Tests maximum timing constraints

### Complex Multi-Phase Tests
10. **complex_multi_phase.csv** (18 events, ordinals 63-80)
    - Complex multi-phase intersection operations
    - Tests advanced phase coordination

11. **cycles_missing_phases.csv** (7 events, ordinals 89-95)
    - Missing yellow phase in cycle
    - Tests phase omission detection

12. **overlapping_phases.csv** (10 events, ordinals 96-105)
    - Phases that overlap in time
    - Tests temporal overlap handling

### Edge Cases and Boundary Conditions
13. **duplicate_events.csv** (5 events, ordinals 106-110)
    - Duplicate phase events at same timestamp
    - Tests event deduplication

14. **zero_duration_phases.csv** (10 events, ordinals 111-120)
    - Phases with zero or near-zero duration
    - Tests minimum duration handling

15. **extremely_long_phases.csv** (10 events, ordinals 121-130)
    - Phases with very long durations (>10 minutes)
    - Tests maximum duration handling

16. **microsecond_precision.csv** (10 events, ordinals 131-140)
    - Events with very precise timing differences
    - Tests high-precision timing

### Error Handling Scenarios
17. **malformed_event_sequence.csv** (10 events, ordinals 141-150)
    - Invalid event sequences (e.g., red before green)
    - Tests sequence validation

18. **missing_phase_starts.csv** (10 events, ordinals 151-160)
    - Events without corresponding phase starts
    - Tests orphaned end events

19. **orphaned_phase_ends.csv** (10 events, ordinals 161-170)
    - Phase end events without starts
    - Tests orphaned phase handling

### Advanced Scenarios
20. **rapid_phase_changes.csv** (10 events, ordinals 171-180)
    - Very rapid phase transitions
    - Tests high-frequency changes

21. **concurrent_multi_phase.csv** (10 events, ordinals 181-190)
    - Multiple phases active simultaneously
    - Tests concurrent phase management

22. **boundary_midnight_crossing.csv** (10 events, ordinals 191-200)
    - Cycles that cross midnight boundary
    - Tests date boundary handling

### Mixed/Invalid Events
23. **invalid_mixed_events.csv** (177 events, ordinals 81-88 + scattered data)
    - Mix of valid and invalid event sequences
    - Contains scattered data points for comprehensive testing
    - Tests robustness against mixed valid/invalid data

## Usage

Each CSV file contains:
- Header row: `datetime,event,param,ordinal_number`
- Data rows with traffic signal events

The files can be used individually for focused testing or combined for comprehensive validation of cycle detection algorithms.

## Event Types
- Event 1: Phase start
- Event 7: Green start  
- Event 8: Yellow start
- Event 9: Red clearance start
- Event 10: Red start 