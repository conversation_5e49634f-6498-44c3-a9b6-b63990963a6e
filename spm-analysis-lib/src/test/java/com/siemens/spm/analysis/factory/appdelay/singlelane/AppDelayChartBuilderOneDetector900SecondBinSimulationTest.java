package com.siemens.spm.analysis.factory.appdelay.singlelane;

import java.time.LocalDateTime;
import java.util.List;

import com.siemens.spm.analysis.factory.AppDelayChartBuilder;
import com.siemens.spm.analysis.factory.AppDelayLaneChartBuilder;
import com.siemens.spm.analysis.factory.appdelay.AppDelayTestUtil;
import com.siemens.spm.analysis.util.AppDelayUtil;
import com.siemens.spm.analysis.util.ChartType;
import com.siemens.spm.analysis.vo.AppDelayChartVO;
import com.siemens.spm.analysis.vo.AppDelayLaneChartVO;
import com.siemens.spm.analysis.vo.AppDelayPlanStatisticsVO;
import com.siemens.spm.analysis.vo.AppDelayVehicleVO;
import com.siemens.spm.perflog.vo.ApproachVO;
import com.siemens.spm.perflog.vo.DetectorVO;
import com.siemens.spm.perflog.vo.IntersectionConfigVO;
import com.siemens.spm.perflog.vo.LaneVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;
import com.siemens.spm.perflog.vo.PhaseVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * Tests the approach delay estimation with values from a traffic simulation.
 *
 * <AUTHOR> Dowideit (<EMAIL>)
 */
// TODO: need to double check it
@Disabled
class AppDelayChartBuilderOneDetector900SecondBinSimulationTest {
    private static final int PHASE_NUM = 1;
    private static final int YEAR = 2021;
    private static final int MONTH = 12;
    private static final int DAY = 9;
    public static final int BIN_SIZE_900 = 900;
    private static final LocalDateTime T0 = LocalDateTime.of(YEAR, MONTH, DAY, 0, 0);
    private static final LocalDateTime T3 = LocalDateTime.of(YEAR, MONTH, DAY, 3, 0);

    private AppDelayChartVO chartVO;

    @BeforeEach
    void setUp() {
        chartVO = AppDelayLaneChartVO.builder()
                .fromTime(T0)
                .toTime(T3.plusMinutes(30))
                .phase(PHASE_NUM)
                .chartType(ChartType.APP_DELAY_LANE.getChartName())
                .build();

        AppDelayChartBuilder builder = new AppDelayLaneChartBuilder(chartVO, BIN_SIZE_900, 0);

        // create config with one lane with "through" movement
        IntersectionConfigVO configVO = IntersectionConfigVO.builder().
                approaches(
                        List.of(
                                ApproachVO.builder()
                                        .phases(
                                                List.of(
                                                        PhaseVO.builder()
                                                                .lanes(
                                                                        List.of(
                                                                                LaneVO.builder()
                                                                                        .movement(
                                                                                                "Through"
                                                                                        )
                                                                                        .detectors(
                                                                                                List.of(DetectorVO
                                                                                                        .builder()
                                                                                                        .detectorNumber(
                                                                                                                1)
                                                                                                        .type(DetectorVO.DetectorType.STOP_BAR)
                                                                                                        .distance(0.0)
                                                                                                        .build())
                                                                                        )
                                                                                        .build()
                                                                        )
                                                                )
                                                                .phaseNumber(PHASE_NUM)
                                                                .build()
                                                )
                                        )
                                        .approachSpeed(31.0686) // 31.0686 mph = 50 kmph
                                        .build()
                        )
                ).build();
        builder.setIntersectionConfig(configVO);

        // Events created from Simulation.
        // Automatically generated from simulation results.
        List<PerfLogEventVO> perfLogEvents = AppDelayUtil
                .readPerflogEventsFromResourcesFile(T0, "appdelay/singlelane/perflog_one_detector.csv",
                        getClass().getClassLoader());
        for (PerfLogEventVO event :
                perfLogEvents) {
            builder.putEvent(event);
        }

        builder.build();
    }

    @Test
    void testAppDelayBinList() {

        // Results extracted from traffic simulation
        List<AppDelayVehicleVO> simulationBins = AppDelayUtil
                .readResultsDelaysFromResourcesFile(T0, "appdelay/singlelane/result_900.csv",
                        getClass().getClassLoader(), true);

        // Estimation results
        List<AppDelayVehicleVO> estimationBins = chartVO.getAppDelayVehicleList();
        assertEquals(14, estimationBins.size());

        // Test bin estimation
        AppDelayTestUtil.testBinEstimation(estimationBins, simulationBins, 3.5, 2);
    }

    @Test
    void testAppDelayPlanStatisticsList() {
        List<AppDelayPlanStatisticsVO> planStatList = chartVO.getAppDelayPlanStatisticsList();
        assertEquals(3, planStatList.size());

        // [00:00:00 - 01:00:02)
        AppDelayPlanStatisticsVO planUnknown = planStatList.get(0);
        double vehicleInPlanUnknown = 31 + 28;
        double totalDelayInPlanUnknown = 9.35 * 31 + 9.85 * 28;
        assertEquals(vehicleInPlanUnknown, planUnknown.getTotalVolume(), 2);
        assertEquals(totalDelayInPlanUnknown / vehicleInPlanUnknown,
                planUnknown.getTotalTimeDelay() / planUnknown.getTotalVolume(), 2.5);

        // [01:00:02, 02:00:02)
        AppDelayPlanStatisticsVO plan1 = planStatList.get(1);
        double vehicleInPlan1 = 49 + 64 + 53 + 48;
        double totalDelayInPlan1 = 10.82 * 49 + 8.03 * 64 + 9.69 * 53 + 7.02 * 48;
        assertEquals(vehicleInPlan1, plan1.getTotalVolume(), 1);
        assertEquals(totalDelayInPlan1 / vehicleInPlan1, plan1.getTotalTimeDelay() / plan1.getTotalVolume(), 2.5);
        assertEquals(1, plan1.getPlan().getPlanNum());

        // [02:00:02, 03:30:00]
        AppDelayPlanStatisticsVO plan2 = planStatList.get(2);
        double vehicleInPlan2 = 23 + 25;
        double totalDelayInPlan2 = 8.27 * 23 + 8.23 * 25;
        assertEquals(vehicleInPlan2, plan2.getTotalVolume(), 1);
        assertEquals(totalDelayInPlan2 / vehicleInPlan2, plan2.getTotalTimeDelay() / plan2.getTotalVolume(), 2.5);
        assertEquals(2, plan2.getPlan().getPlanNum());

    }

}
