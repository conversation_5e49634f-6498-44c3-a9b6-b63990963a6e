package com.siemens.spm.analysis.vo.splitmonitor;

import com.siemens.spm.perflog.vo.PerfLogEventVO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvFileSource;
import org.junit.jupiter.params.provider.ValueSource;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * Comprehensive unit tests for the CyclesFactory class.
 * <p>
 * This test suite covers all public methods with various input scenarios including:
 * - Valid inputs with expected outputs
 * - Boundary conditions (empty collections, null values, minimum/maximum values)
 * - Invalid inputs that should trigger exceptions
 * - Edge cases specific to the business logic
 *
 * <AUTHOR> Suite Generator
 * @version 1.0
 * @since 2025-07-02
 */
@Slf4j
@DisplayName("CyclesFactory Tests")
class CyclesFactoryTest {

    /**
     * Loads test data from a specific scenario CSV file.
     * Each scenario now has its own dedicated CSV file in the scenarios directory.
     *
     * @param scenarioName the scenario name (corresponds to CSV filename without extension)
     * @return list of PerfLogEventVO objects for the scenario
     * @throws IOException if CSV file cannot be read
     */
    private List<PerfLogEventVO> loadTestDataFromCsv(String scenarioName) throws IOException {
        String csvPath = "/test-data/scenarios/" + scenarioName + ".csv";
        List<CsvTestDataRow> scenarioData = loadCsvTestDataFromFile(csvPath);
        return scenarioData.stream()
                .sorted(Comparator.comparingInt(a -> a.ordinalNumber))
                .map(this::convertToPerLogEventVO)
                .toList();
    }

    /**
     * Loads test data from CSV file and returns events for a specific ordinal number range.
     * This method is kept for backward compatibility and comprehensive dataset analysis.
     *
     * @param startOrdinal the starting ordinal number (inclusive)
     * @param endOrdinal   the ending ordinal number (inclusive)
     * @return list of PerfLogEventVO objects for the range
     * @throws IOException if CSV file cannot be read
     */
    private List<PerfLogEventVO> loadTestDataFromCsv(int startOrdinal, int endOrdinal) throws IOException {
        List<CsvTestDataRow> allData = loadAllCsvTestData();
        return allData.stream()
                .filter(row -> row.ordinalNumber >= startOrdinal && row.ordinalNumber <= endOrdinal)
                .sorted(Comparator.comparingInt(a -> a.ordinalNumber)).map(this::convertToPerLogEventVO)
                .toList();
    }

    /**
     * Loads test data from a specific CSV file path.
     *
     * @param csvPath the path to the CSV file (relative to resources)
     * @return list of CSV test data rows
     * @throws IOException if CSV file cannot be read
     */
    private List<CsvTestDataRow> loadCsvTestDataFromFile(String csvPath) throws IOException {
        List<CsvTestDataRow> data = new ArrayList<>();

        try (InputStream inputStream = getClass().getResourceAsStream(csvPath);
                BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {

            if (inputStream == null) {
                throw new IOException("CSV file not found: " + csvPath);
            }

            String line;
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                // Skip comments, empty lines, and header
                if (line.isEmpty() || line.startsWith("#") || line.startsWith("datetime,event,param,ordinal_number")) {
                    continue;
                }

                String[] parts = line.split(",");
                if (parts.length >= 4) {
                    try {
                        // Parse datetime with timezone and convert to LocalDateTime
                        // Format: "2025-02-18 09:59:45.200 +0700"
                        String datetimeStr = parts[0].trim();
                        LocalDateTime datetime = parseTimestampWithTimezone(datetimeStr);

                        CsvTestDataRow row = new CsvTestDataRow(datetime, Integer.parseInt(parts[1].trim()),
                                // event ID
                                Long.parseLong(parts[2].trim()),    // param (phase)
                                Integer.parseInt(parts[3].trim())   // ordinal_number
                        );
                        data.add(row);
                    } catch (Exception e) {
                        // Skip malformed rows
                        log.warn("Skipping malformed CSV row: {} - {}", line, e.getMessage());
                    }
                }
            }
        }

        return data;
    }

    /**
     * Loads all test data from the original combined CSV file.
     * This method is kept for comprehensive dataset analysis.
     *
     * @return list of all CSV test data rows
     * @throws IOException if CSV file cannot be read
     */
    private List<CsvTestDataRow> loadAllCsvTestData() throws IOException {
        return loadCsvTestDataFromFile("/test-data/cycles-factory-test-data.csv");
    }

    /**
     * Parses a timestamp string with timezone and converts to LocalDateTime.
     * Format: "YYYY-MM-DD HH:mm:ss.SSS +ZZZZ"
     *
     * @param timestampStr the timestamp string with timezone
     * @return LocalDateTime (timezone information is discarded for testing purposes)
     */
    private LocalDateTime parseTimestampWithTimezone(String timestampStr) {
        try {
            // Parse the full timestamp with timezone
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS Z");
            ZonedDateTime zonedDateTime = ZonedDateTime.parse(timestampStr, formatter);
            // Convert to LocalDateTime (discarding timezone for testing)
            return zonedDateTime.toLocalDateTime();
        } catch (Exception e) {
            // Fallback: try parsing without milliseconds
            try {
                DateTimeFormatter fallbackFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss Z");
                ZonedDateTime zonedDateTime = ZonedDateTime.parse(timestampStr, fallbackFormatter);
                return zonedDateTime.toLocalDateTime();
            } catch (Exception e2) {
                throw new IllegalArgumentException("Unable to parse timestamp: " + timestampStr, e2);
            }
        }
    }

    /**
     * Converts a CSV test data row to a PerfLogEventVO object.
     * Uses numeric event ID to map to PerfLogEventVO.Event enum.
     *
     * @param row the CSV data row
     * @return PerfLogEventVO object
     */
    private PerfLogEventVO convertToPerLogEventVO(CsvTestDataRow row) {
        PerfLogEventVO event = new PerfLogEventVO();
        event.setDateTime(row.datetime);
        event.setParameter(row.param);

        // Convert numeric event ID to enum using PerfLogEventVO.Event.of() method
        PerfLogEventVO.Event eventEnum = PerfLogEventVO.Event.of(row.eventId);
        if (eventEnum != null) {
            event.setEvent(eventEnum);
        } else {
            // Handle unknown event IDs - default to PHASE_ON
            log.warn("Unknown event ID: {}, defaulting to PHASE_ON", row.eventId);
            event.setEvent(PerfLogEventVO.Event.PHASE_ON);
        }
        event.setOrdinal(row.ordinalNumber);

        return event;
    }

    /**
     * Gets all available test scenarios.
     * Returns the list of scenario names that correspond to individual CSV files.
     *
     * @return list of scenario names
     */
    private List<String> getAvailableScenarios() {
        return List.of("complete_single_cycle", "multiple_cycles_phase1", "multi_phase_simultaneous",
                "incomplete_missing_red_end", "incomplete_missing_yellow", "out_of_order_events", "boundary_same_time",
                "short_cycle_times", "long_cycle_times", "complex_multi_phase", "invalid_mixed_events",
                // New edge cases for cycle detection
                "cycles_missing_phases", "overlapping_phases", "duplicate_events",
                // New boundary conditions for timing analysis
                "zero_duration_phases", "extremely_long_phases", "microsecond_precision",
                // New error handling scenarios
                "malformed_event_sequence", "missing_phase_starts", "orphaned_phase_ends",
                // New additional complex scenarios
                "rapid_phase_changes", "concurrent_multi_phase", "boundary_midnight_crossing");
    }

    /**
     * Data class to represent a row from the CSV test data file.
     * Format: datetime,event,param,ordinal_number
     * - datetime: Full timestamp with timezone (YYYY-MM-DD HH:mm:ss.SSS +ZZZZ)
     * - event: Numeric event type ID (PerfLogEventVO.Event enum ordinal values)
     * - param: Phase/parameter number
     * - ordinal_number: Sequence number for event ordering
     */
    private record CsvTestDataRow(LocalDateTime datetime, int eventId, long param, int ordinalNumber) {

    }

    @Nested
    @DisplayName("CSV Data Loading Tests")
    class CsvDataLoadingTests {

        @ParameterizedTest
        @CsvFileSource(resources = "/test-data/csv-loading/basic-scenarios-test-cases.csv", numLinesToSkip = 1)
        @DisplayName("Should load CSV test data successfully")
        void shouldLoadCsvTestDataSuccessfully(String scenario, boolean shouldExist, int expectedMinEvents, 
                int expectedMaxEvents) throws IOException {
            log.info("Testing CSV data loading for scenario: {}", scenario);
            
            if (shouldExist) {
                // When
                List<PerfLogEventVO> events = loadTestDataFromCsv(scenario);
                
                // Then
                assertNotNull(events, "Events should not be null for scenario: " + scenario);
                assertTrue(events.size() >= expectedMinEvents, 
                        String.format("Should have at least %d events for scenario %s, got %d", 
                                expectedMinEvents, scenario, events.size()));
                assertTrue(events.size() <= expectedMaxEvents, 
                        String.format("Should have at most %d events for scenario %s, got %d", 
                                expectedMaxEvents, scenario, events.size()));
                
                log.info("✓ Loaded scenario '{}': {} events", scenario, events.size());

                // Verify events have required fields
                for (PerfLogEventVO event : events) {
                    assertNotNull(event.getDateTime(), "Event datetime should not be null");
                    assertNotNull(event.getEvent(), "Event type should not be null");
                    assertTrue(event.getParameter() >= 0, "Event parameter should be non-negative");
                }
            } else {
                // Test that non-existent scenarios are handled appropriately
                try {
                    loadTestDataFromCsv(scenario);
                    // If we get here, the scenario unexpectedly exists
                    assertTrue(false, "Scenario " + scenario + " should not exist but was loaded successfully");
                } catch (IOException e) {
                    // Expected - scenario doesn't exist
                    log.info("✓ Correctly failed to load non-existent scenario: {}", scenario);
                }
            }
        }

        @ParameterizedTest
        @ValueSource(strings = {
            "complete_single_cycle", "multiple_cycles_phase1", "multi_phase_simultaneous",
            "incomplete_missing_red_end", "incomplete_missing_yellow", "out_of_order_events", 
            "boundary_same_time", "short_cycle_times", "long_cycle_times", "complex_multi_phase", 
            "invalid_mixed_events", "cycles_missing_phases", "overlapping_phases", "duplicate_events",
            "zero_duration_phases", "extremely_long_phases", "microsecond_precision",
            "malformed_event_sequence", "missing_phase_starts", "orphaned_phase_ends",
            "rapid_phase_changes", "concurrent_multi_phase", "boundary_midnight_crossing"
        })
        @DisplayName("Should load individual scenario file successfully")
        void shouldLoadIndividualScenarioFileSuccessfully(String scenario) throws IOException {
            log.info("Testing individual scenario loading: {}", scenario);
            
            // When
            List<PerfLogEventVO> events = loadTestDataFromCsv(scenario);
            
            // Then
            assertNotNull(events, "Events should not be null for scenario: " + scenario);
            log.info("✓ Loaded scenario '{}': {} events", scenario, events.size());

            // Verify events have required fields
            for (PerfLogEventVO event : events) {
                assertNotNull(event.getDateTime(), "Event datetime should not be null");
                assertNotNull(event.getEvent(), "Event type should not be null");
                assertTrue(event.getParameter() >= 0, "Event parameter should be non-negative");
            }
        }

        @ParameterizedTest
        @CsvFileSource(resources = "/test-data/csv-loading/data-validation-test-cases.csv", numLinesToSkip = 1)
        @DisplayName("Should validate loaded scenario data correctly")
        void shouldValidateLoadedScenarioDataCorrectly(String scenario, boolean expectSortedByTime, 
                boolean expectPhase1Events, boolean expectPhase2Events, int expectedUniquePhases) throws IOException {
            log.info("Testing data validation for scenario: {}", scenario);
            
            // When
            List<PerfLogEventVO> events = loadTestDataFromCsv(scenario);

            // Then
            assertNotNull(events, "Events should not be null");
            assertFalse(events.isEmpty(), "Events should not be empty");

            // Verify events are sorted by time if expected
            if (expectSortedByTime) {
                for (int i = 1; i < events.size(); i++) {
                    PerfLogEventVO prev = events.get(i - 1);
                    PerfLogEventVO curr = events.get(i);
                    assertTrue(prev.getDateTime().isBefore(curr.getDateTime()) || prev.getDateTime()
                            .equals(curr.getDateTime()), "Events should be sorted by datetime");
                }
            }

            // Verify expected phase presence
            boolean hasPhase1 = events.stream().anyMatch(e -> e.getParameter() == 1L);
            boolean hasPhase2 = events.stream().anyMatch(e -> e.getParameter() == 2L);
            
            if (expectPhase1Events) {
                assertTrue(hasPhase1, "Should contain phase 1 events for scenario: " + scenario);
            }
            if (expectPhase2Events) {
                assertTrue(hasPhase2, "Should contain phase 2 events for scenario: " + scenario);
            }

            // Verify unique phases count
            long uniquePhases = events.stream().mapToLong(PerfLogEventVO::getParameter).distinct().count();
            assertTrue(uniquePhases == expectedUniquePhases, 
                    String.format("Should have exactly %d unique phases for scenario %s, got %d", 
                            expectedUniquePhases, scenario, uniquePhases));

            log.info("✓ Data validation passed for scenario: {} (phases: {}, sorted: {})", 
                    scenario, uniquePhases, expectSortedByTime);
        }

        @Test
        @DisplayName("Should load all individual scenario files successfully")
        void shouldLoadAllIndividualScenarioFilesSuccessfully() throws IOException {
            log.info("\n{}", "=".repeat(80));
            log.info("TESTING INDIVIDUAL SCENARIO FILE LOADING");
            log.info("=".repeat(80));

            List<String> scenarios = getAvailableScenarios();
            int totalEvents = 0;
            int successfulLoads = 0;

            for (String scenario : scenarios) {
                try {
                    List<PerfLogEventVO> events = loadTestDataFromCsv(scenario);
                    assertNotNull(events, "Events should not be null for scenario: " + scenario);

                    log.info("✓ Loaded scenario '{}': {} events", scenario, events.size());
                    totalEvents += events.size();
                    successfulLoads++;

                    // Verify events have required fields
                    for (PerfLogEventVO event : events) {
                        assertNotNull(event.getDateTime(), "Event datetime should not be null");
                        assertNotNull(event.getEvent(), "Event type should not be null");
                        assertTrue(event.getParameter() >= 0, "Event parameter should be non-negative");
                    }

                } catch (IOException e) {
                    log.error("✗ Failed to load scenario '{}': {}", scenario, e.getMessage());
                    throw e;
                }
            }

            log.info("\n📊 SUMMARY:");
            log.info("✓ Successfully loaded {}/{} scenario files", successfulLoads, scenarios.size());
            log.info("✓ Total events loaded: {}", totalEvents);
            log.info("✓ Average events per scenario: {}", totalEvents / scenarios.size());

            // Validate all scenarios loaded successfully
            assertTrue(successfulLoads == scenarios.size(),
                    "All scenario files should load successfully");
            assertTrue(totalEvents > 0, "Should have loaded some events");
        }

        /**
         * Comprehensive test that loads the entire CSV dataset and provides detailed analysis.
         * This test verifies that the new realistic CSV format is working correctly and
         * provides insights into the traffic signal cycle patterns in the test data.
         */
        @Test
        @DisplayName("Should analyze complete dataset and display summary")
        void shouldAnalyzeCompleteDatasetAndDisplaySummary() throws IOException {
            log.info("\n{}", "=".repeat(80));
            log.info("COMPREHENSIVE TRAFFIC SIGNAL CYCLE ANALYSIS");
            log.info("=".repeat(80));

            // 1. Load Complete Dataset
            log.info("\n1. LOADING COMPLETE DATASET");
            log.info("-".repeat(40));

            List<CsvTestDataRow> allCsvData = loadAllCsvTestData();
            List<PerfLogEventVO> allEvents = allCsvData.stream()
                    .sorted(Comparator.comparingInt(a -> a.ordinalNumber))
                    .map(CyclesFactoryTest.this::convertToPerLogEventVO).toList();

            log.info("✓ Loaded {} events from CSV (ordinal numbers 0-{})", allEvents.size(),
                    allCsvData.stream().mapToInt(row -> row.ordinalNumber).max().orElse(0));

            // Display event type distribution
            Map<PerfLogEventVO.Event, Long> eventCounts = allEvents.stream()
                    .collect(Collectors.groupingBy(PerfLogEventVO::getEvent, Collectors.counting()));

            log.debug("\nEvent Type Distribution:");
            eventCounts.entrySet().stream().sorted(Map.Entry.<PerfLogEventVO.Event, Long>comparingByValue().reversed())
                    .forEach(entry -> log.debug("  {}: {} events", entry.getKey().name(), entry.getValue()));

            // 2. Process Through CyclesFactory
            log.info("\n2. PROCESSING THROUGH CYCLES FACTORY");
            log.info("-".repeat(40));

            List<CycleResult> results = CyclesFactory.calculateCycle(allEvents,
                    allEvents.get(allEvents.size() - 1).getDateTime());

            log.info("✓ Generated {} cycle analysis results", results.size());

            // 3. Display Summary Statistics
            log.info("\n3. SUMMARY STATISTICS");
            log.info("-".repeat(40));

            if (results.isEmpty()) {
                log.warn("⚠ No cycles detected - this may indicate issues with the data or processing logic");
                // Still run basic validation
                assertNotNull(results, "Results should not be null");
                return;
            }

            // Calculate statistics
            int totalCycles = results.size();
            int completeCycles = (int) results.stream().filter(c -> c.getRedEnd() != null).count();
            int incompleteCycles = totalCycles - completeCycles;

            List<Double> cycleLengths = results.stream().map(CycleResult::getPhaseLength)
                    .filter(phaseLength -> phaseLength > 0).toList();

            double avgCycleLength = cycleLengths.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
            double minCycleLength = cycleLengths.stream().mapToDouble(Double::doubleValue).min().orElse(0.0);
            double maxCycleLength = cycleLengths.stream().mapToDouble(Double::doubleValue).max().orElse(0.0);

            log.info("Total Cycles Detected    : {}", totalCycles);
            log.info("Complete Cycles          : {}", completeCycles);
            log.info("Incomplete Cycles        : {}", incompleteCycles);
            log.info("Average Cycle Length     : {} seconds ({} minutes)",
                    String.format("%.1f", avgCycleLength), String.format("%.1f", avgCycleLength / 60.0));
            log.info("Min Cycle Length         : {} seconds", String.format("%.1f", minCycleLength));
            log.info("Max Cycle Length         : {} seconds", String.format("%.1f", maxCycleLength));

            // Phase distribution
            Map<Long, Long> phaseDistribution = results.stream()
                    .collect(Collectors.groupingBy(CycleResult::getPhase, Collectors.counting()));

            log.debug("\nCycles by Phase:");
            phaseDistribution.entrySet().stream().sorted(Map.Entry.comparingByKey())
                    .forEach(entry -> log.debug("  Phase {}: {} cycles", entry.getKey(), entry.getValue()));

            // 4. Phase Timing Analysis
            log.info("\n4. PHASE TIMING ANALYSIS");
            log.info("-".repeat(40));
            log.debug("%-8s %-12s %-12s %-12s %-12s %-15s", "Phase", "Green (s)", "Yellow (s)", "Red (s)",
                    "Total (s)", "Cycle Start");
            log.info("-".repeat(80));

            for (CycleResult cycle : results.stream().limit(10).toList()) { // Show first 10 cycles
                log.debug("%-8d %-12.1f %-12.1f %-12.1f %-12.1f {}", cycle.getPhase(), cycle.getGreenLength(),
                        cycle.getYellowLength(), cycle.getRedLength(), cycle.getPhaseLength(),
                        cycle.getGreenStart() != null ? cycle.getGreenStart().toString() : "N/A");
            }

            if (results.size() > 10) {
                log.info("... and {} more cycles", results.size() - 10);
            }

            // Test Validation
            log.info("\n6. TEST VALIDATION");
            log.info("-".repeat(40));

            // Basic assertions
            assertNotNull(results, "Results should not be null");
            assertFalse(results.isEmpty(), "At least some cycles should be detected from complete dataset");

            // Verify cycle lengths are reasonable (between 5 seconds and 10 minutes for most cases)
            // Allow for edge cases in test data that may have shorter cycles
            for (CycleResult cycle : results) {
                if (cycle.getPhaseLength() > 0) {
                    assertTrue(cycle.getPhaseLength() >= 1.0,
                            String.format("Cycle length should be at least 1 second, got %.1f for phase %d",
                                    cycle.getPhaseLength(), cycle.getPhase()));
                    assertTrue(cycle.getPhaseLength() <= 172800.0,
                            String.format("Cycle length should be at most 48 hours, got %.1f for phase %d",
                                    cycle.getPhaseLength(), cycle.getPhase()));
                }
            }

            // Verify phase sequences follow expected patterns
            int validSequences = 0;
            for (CycleResult cycle : results) {
                if (isValidPhaseSequence(cycle)) {
                    validSequences++;
                }
            }

            log.info("✓ {}/{} cycles have valid phase sequences (G→Y→R pattern)", validSequences,
                    results.size());
            log.info("✓ All cycle lengths are within reasonable ranges");
            log.info("✓ Complete dataset analysis completed successfully");

            log.info("\n{}", "=".repeat(80));
            log.info("ANALYSIS COMPLETE");
            log.info("=".repeat(80));
        }

        /**
         * Validates that a cycle follows the expected G→Y→R phase sequence pattern.
         */
        private boolean isValidPhaseSequence(CycleResult cycle) {
            // Check if we have the minimum required timestamps
            if (cycle.getGreenStart() == null) {
                return false;
            }

            // If we have green end, it should be after green start
            if (cycle.getGreenEnd() != null && cycle.getGreenEnd().isBefore(cycle.getGreenStart())) {
                return false;
            }

            // If we have yellow start, it should be after green start
            if (cycle.getYellowStart() != null && cycle.getYellowStart().isBefore(cycle.getGreenStart())) {
                return false;
            }

            // If we have yellow end, it should be after yellow start
            if (cycle.getYellowStart() != null && cycle.getYellowEnd() != null && cycle.getYellowEnd()
                    .isBefore(cycle.getYellowStart())) {
                return false;
            }

            // If we have red start, it should be after green start
            return cycle.getRedStart() == null || !cycle.getRedStart().isBefore(cycle.getGreenStart());
        }

    }

    @Nested
    @DisplayName("Parameterized Cycle Analysis Tests")
    class ParameterizedCycleAnalysisTests {

        @ParameterizedTest
        @CsvFileSource(resources = "/test-data/cycle-analysis-expectations.csv", numLinesToSkip = 1)
        @DisplayName("Should analyze cycles according to expected results")
        void shouldAnalyzeCyclesAccordingToExpectedResults(String scenarioName, int expectedMinCycles, 
                int expectedMaxCycles, double expectedMinCycleLength, double expectedMaxCycleLength) throws IOException {
            log.info("Testing cycle analysis for scenario: {}", scenarioName);
            
            // Given
            List<PerfLogEventVO> events = loadTestDataFromCsv(scenarioName);
            assertNotNull(events, "Events should not be null for scenario: " + scenarioName);
            assertFalse(events.isEmpty(), "Events should not be empty for scenario: " + scenarioName);
            
            // When
            List<CycleResult> results = CyclesFactory.calculateCycle(events, 
                    events.get(events.size() - 1).getDateTime());
            
            // Then
            assertNotNull(results, "Results should not be null");
            
            // Validate cycle count expectations
            assertTrue(results.size() >= expectedMinCycles, 
                    String.format("Should have at least %d cycles for scenario %s, got %d", 
                            expectedMinCycles, scenarioName, results.size()));
            assertTrue(results.size() <= expectedMaxCycles, 
                    String.format("Should have at most %d cycles for scenario %s, got %d", 
                            expectedMaxCycles, scenarioName, results.size()));
            
            // Validate cycle length expectations for cycles with positive length
            for (CycleResult cycle : results) {
                if (cycle.getPhaseLength() > 0) {
                    assertTrue(cycle.getPhaseLength() >= expectedMinCycleLength, 
                            String.format("Cycle length %.2f should be at least %.2f for scenario %s", 
                                    cycle.getPhaseLength(), expectedMinCycleLength, scenarioName));
                    assertTrue(cycle.getPhaseLength() <= expectedMaxCycleLength, 
                            String.format("Cycle length %.2f should be at most %.2f for scenario %s", 
                                    cycle.getPhaseLength(), expectedMaxCycleLength, scenarioName));
                }
            }
            
            log.info("✓ Scenario '{}' passed: {} cycles, lengths {}-{} seconds", 
                    scenarioName, results.size(), expectedMinCycleLength, expectedMaxCycleLength);
        }

        @ParameterizedTest
        @ValueSource(strings = {
            "complete_single_cycle", "multiple_cycles_phase1", "complex_multi_phase"
        })
        @DisplayName("Should generate valid cycle results for valid scenarios")
        void shouldGenerateValidCycleResultsForValidScenarios(String scenarioName) throws IOException {
            log.info("Testing valid cycle generation for scenario: {}", scenarioName);
            
            // Given
            List<PerfLogEventVO> events = loadTestDataFromCsv(scenarioName);
            
            // When
            List<CycleResult> results = CyclesFactory.calculateCycle(events, 
                    events.get(events.size() - 1).getDateTime());
            
            // Then
            assertNotNull(results, "Results should not be null");
            
            // For valid scenarios, we should get at least one cycle
            assertFalse(results.isEmpty(), "Should generate at least one cycle for valid scenario: " + scenarioName);
            
            // Validate that each cycle has reasonable properties
            for (CycleResult cycle : results) {
                assertTrue(cycle.getPhase() > 0, "Phase number should be positive");
                assertTrue(cycle.getPhaseLength() >= 0, "Phase length should be non-negative");
                
                if (cycle.getGreenStart() != null && cycle.getGreenEnd() != null) {
                    assertTrue(!cycle.getGreenEnd().isBefore(cycle.getGreenStart()), 
                            "Green end should not be before green start");
                }
            }
            
            log.info("✓ Generated {} valid cycles for scenario '{}'", results.size(), scenarioName);
        }

        @ParameterizedTest
        @CsvFileSource(resources = "/test-data/phase-timing-validation.csv", numLinesToSkip = 1)
        @DisplayName("Should validate phase timing according to traffic signal rules")
        void shouldValidatePhaseTimingAccordingToTrafficSignalRules(String scenarioName, 
                double expectedMinGreenTime, double expectedMaxGreenTime,
                double expectedMinYellowTime, double expectedMaxYellowTime,
                double expectedMinRedTime, double expectedMaxRedTime) throws IOException {
            log.info("Testing phase timing validation for scenario: {}", scenarioName);
            
            // Given
            List<PerfLogEventVO> events = loadTestDataFromCsv(scenarioName);
            
            // When
            List<CycleResult> results = CyclesFactory.calculateCycle(events, 
                    events.get(events.size() - 1).getDateTime());
            
            // Then
            assertNotNull(results, "Results should not be null");
            
            // Validate phase timing for each cycle
            for (CycleResult cycle : results) {
                // Validate green phase timing
                if (cycle.getGreenLength() > 0) {
                    assertTrue(cycle.getGreenLength() >= expectedMinGreenTime, 
                            String.format("Green phase %.2fs should be at least %.2fs for scenario %s", 
                                    cycle.getGreenLength(), expectedMinGreenTime, scenarioName));
                    assertTrue(cycle.getGreenLength() <= expectedMaxGreenTime, 
                            String.format("Green phase %.2fs should be at most %.2fs for scenario %s", 
                                    cycle.getGreenLength(), expectedMaxGreenTime, scenarioName));
                }
                
                // Validate yellow phase timing
                if (cycle.getYellowLength() > 0) {
                    assertTrue(cycle.getYellowLength() >= expectedMinYellowTime, 
                            String.format("Yellow phase %.2fs should be at least %.2fs for scenario %s", 
                                    cycle.getYellowLength(), expectedMinYellowTime, scenarioName));
                    assertTrue(cycle.getYellowLength() <= expectedMaxYellowTime, 
                            String.format("Yellow phase %.2fs should be at most %.2fs for scenario %s", 
                                    cycle.getYellowLength(), expectedMaxYellowTime, scenarioName));
                }
                
                // Validate red phase timing
                if (cycle.getRedLength() > 0) {
                    assertTrue(cycle.getRedLength() >= expectedMinRedTime, 
                            String.format("Red phase %.2fs should be at least %.2fs for scenario %s", 
                                    cycle.getRedLength(), expectedMinRedTime, scenarioName));
                    assertTrue(cycle.getRedLength() <= expectedMaxRedTime, 
                            String.format("Red phase %.2fs should be at most %.2fs for scenario %s", 
                                    cycle.getRedLength(), expectedMaxRedTime, scenarioName));
                }
            }
            
            log.info("✓ Phase timing validation passed for scenario '{}'", scenarioName);
        }
    }

    @Nested
    @DisplayName("Edge Cases for Cycle Detection")
    class EdgeCasesForCycleDetectionTests {

        @ParameterizedTest
        @CsvFileSource(resources = "/test-data/edge-cases/missing-phases-test-cases.csv", numLinesToSkip = 1)
        @DisplayName("Should handle cycles with missing phases gracefully")
        void shouldHandleCyclesWithMissingPhasesGracefully(String scenarioName, int expectedMinCycles, 
                int expectedMaxCycles, boolean shouldAllowMissingYellow, boolean shouldAllowMissingRed) throws IOException {
            log.info("Testing missing phases scenario: {}", scenarioName);

            // Given
            List<PerfLogEventVO> events = loadTestDataFromCsv(scenarioName);
            log.info("✓ Loaded {} events for missing phases scenario: {}", events.size(), scenarioName);

            // When
            List<CycleResult> results = CyclesFactory.calculateCycle(events,
                    events.get(events.size() - 1).getDateTime());

            // Then
            log.info("✓ Generated {} cycle results for scenario: {}", results.size(), scenarioName);
            assertNotNull(results, "Results should not be null even with missing phases");
            
            // Validate cycle count expectations
            assertTrue(results.size() >= expectedMinCycles, 
                    String.format("Should have at least %d cycles for scenario %s, got %d", 
                            expectedMinCycles, scenarioName, results.size()));
            assertTrue(results.size() <= expectedMaxCycles, 
                    String.format("Should have at most %d cycles for scenario %s, got %d", 
                            expectedMaxCycles, scenarioName, results.size()));

            // Validate missing phase handling
            for (CycleResult cycle : results) {
                if (!shouldAllowMissingYellow) {
                    assertTrue(cycle.getYellowLength() > 0 || cycle.getYellowStart() != null, 
                            "Yellow phase should be present when not allowed to be missing");
                }
                if (!shouldAllowMissingRed) {
                    assertTrue(cycle.getRedLength() > 0 || cycle.getRedStart() != null, 
                            "Red phase should be present when not allowed to be missing");
                }
            }

            log.info("✓ Missing phases test passed for scenario: {}", scenarioName);
        }

        @ParameterizedTest
        @CsvFileSource(resources = "/test-data/edge-cases/overlapping-phases-test-cases.csv", numLinesToSkip = 1)
        @DisplayName("Should detect overlapping phases correctly")
        void shouldDetectOverlappingPhasesCorrectly(String scenarioName, int expectedCycles, 
                boolean expectOverlaps, int maxAllowedOverlaps) throws IOException {
            log.info("Testing overlapping phases scenario: {}", scenarioName);

            // Given
            List<PerfLogEventVO> events = loadTestDataFromCsv(scenarioName);
            log.info("✓ Loaded {} events for overlapping phases scenario: {}", events.size(), scenarioName);

            // When
            List<CycleResult> results = CyclesFactory.calculateCycle(events,
                    events.get(events.size() - 1).getDateTime());

            // Then
            log.info("✓ Generated {} cycle results for scenario: {}", results.size(), scenarioName);
            assertNotNull(results, "Results should not be null with overlapping phases");
            
            if (expectedCycles > 0) {
                assertTrue(results.size() == expectedCycles, 
                        String.format("Should have exactly %d cycles for scenario %s, got %d", 
                                expectedCycles, scenarioName, results.size()));
            }

            // Check for phase overlaps
            int actualOverlaps = 0;
            for (int i = 0; i < results.size() - 1; i++) {
                CycleResult current = results.get(i);
                CycleResult next = results.get(i + 1);

                if (current.getRedEnd() != null && next.getGreenStart() != null) {
                    boolean hasOverlap = current.getRedEnd().isAfter(next.getGreenStart());
                    if (hasOverlap) {
                        actualOverlaps++;
                    }
                }
            }

            if (expectOverlaps) {
                assertTrue(actualOverlaps > 0, "Should detect overlaps when expected");
            }
            assertTrue(actualOverlaps <= maxAllowedOverlaps, 
                    String.format("Should not exceed %d overlaps, found %d", maxAllowedOverlaps, actualOverlaps));

            log.info("✓ Overlapping phases test passed for scenario: {} (found {} overlaps)", scenarioName, actualOverlaps);
        }

        @ParameterizedTest
        @CsvFileSource(resources = "/test-data/edge-cases/duplicate-events-test-cases.csv", numLinesToSkip = 1)
        @DisplayName("Should handle duplicate events appropriately")
        void shouldHandleDuplicateEventsAppropriately(String scenarioName, int expectedMinCycles, 
                int expectedMaxCycles, int expectedDuplicateGroups) throws IOException {
            log.info("Testing duplicate events scenario: {}", scenarioName);

            // Given
            List<PerfLogEventVO> events = loadTestDataFromCsv(scenarioName);
            log.info("✓ Loaded {} events for duplicate events scenario: {}", events.size(), scenarioName);

            // When
            List<CycleResult> results = CyclesFactory.calculateCycle(events,
                    events.get(events.size() - 1).getDateTime());

            // Then
            log.info("✓ Generated {} cycle results for scenario: {}", results.size(), scenarioName);
            assertNotNull(results, "Results should not be null with duplicate events");

            // Validate cycle count expectations
            assertTrue(results.size() >= expectedMinCycles, 
                    String.format("Should have at least %d cycles for scenario %s, got %d", 
                            expectedMinCycles, scenarioName, results.size()));
            assertTrue(results.size() <= expectedMaxCycles, 
                    String.format("Should have at most %d cycles for scenario %s, got %d", 
                            expectedMaxCycles, scenarioName, results.size()));

            // Analyze duplicate detection
            Map<String, Long> eventCounts = events.stream()
                    .collect(Collectors.groupingBy(
                            e -> e.getEvent().name() + "_" + e.getParameter() + "_" + e.getDateTime(),
                            Collectors.counting()));

            long actualDuplicateGroups = eventCounts.values().stream().filter(count -> count > 1).count();
            
            if (expectedDuplicateGroups >= 0) {
                assertTrue(actualDuplicateGroups == expectedDuplicateGroups, 
                        String.format("Should have exactly %d duplicate groups for scenario %s, found %d", 
                                expectedDuplicateGroups, scenarioName, actualDuplicateGroups));
            }

            log.info("✓ Duplicate events test passed for scenario: {} (found {} duplicate groups)", 
                    scenarioName, actualDuplicateGroups);
        }

    }

    @Nested
    @DisplayName("Boundary Conditions for Timing Analysis")
    class BoundaryConditionsForTimingAnalysisTests {

        @ParameterizedTest
        @CsvFileSource(resources = "/test-data/boundary-conditions/zero-duration-test-cases.csv", numLinesToSkip = 1)
        @DisplayName("Should handle zero duration phases correctly")
        void shouldHandleZeroDurationPhasesCorrectly(String scenarioName, int expectedMinCycles, 
                int expectedMaxCycles, double minAllowedDuration, boolean allowZeroDuration) throws IOException {
            log.info("Testing zero duration phases scenario: {}", scenarioName);

            // Given
            List<PerfLogEventVO> events = loadTestDataFromCsv(scenarioName);
            log.info("✓ Loaded {} events for zero duration phases scenario: {}", events.size(), scenarioName);

            // When
            List<CycleResult> results = CyclesFactory.calculateCycle(events,
                    events.get(events.size() - 1).getDateTime());

            // Then
            log.info("✓ Generated {} cycle results for scenario: {}", results.size(), scenarioName);
            assertNotNull(results, "Results should not be null with zero duration phases");

            // Validate cycle count expectations
            assertTrue(results.size() >= expectedMinCycles, 
                    String.format("Should have at least %d cycles for scenario %s, got %d", 
                            expectedMinCycles, scenarioName, results.size()));
            assertTrue(results.size() <= expectedMaxCycles, 
                    String.format("Should have at most %d cycles for scenario %s, got %d", 
                            expectedMaxCycles, scenarioName, results.size()));

            // Validate that zero durations are handled appropriately
            for (CycleResult cycle : results) {
                assertTrue(cycle.getPhaseLength() >= 0, "Phase length should not be negative");
                
                if (!allowZeroDuration && cycle.getPhaseLength() > 0) {
                    assertTrue(cycle.getPhaseLength() >= minAllowedDuration, 
                            String.format("Phase length %.3f should be at least %.3f for scenario %s", 
                                    cycle.getPhaseLength(), minAllowedDuration, scenarioName));
                }
            }

            log.info("✓ Zero duration phases test passed for scenario: {}", scenarioName);
        }

        @ParameterizedTest
        @CsvFileSource(resources = "/test-data/boundary-conditions/long-phases-test-cases.csv", numLinesToSkip = 1)
        @DisplayName("Should handle extremely long phases correctly")
        void shouldHandleExtremelyLongPhasesCorrectly(String scenarioName, int expectedCycles, 
                double minExpectedDuration, double maxExpectedDuration) throws IOException {
            log.info("Testing extremely long phases scenario: {}", scenarioName);

            // Given
            List<PerfLogEventVO> events = loadTestDataFromCsv(scenarioName);
            log.info("✓ Loaded {} events for extremely long phases scenario: {}", events.size(), scenarioName);

            // When
            List<CycleResult> results = CyclesFactory.calculateCycle(events,
                    events.get(events.size() - 1).getDateTime());

            // Then
            log.info("✓ Generated {} cycle results for scenario: {}", results.size(), scenarioName);
            assertNotNull(results, "Results should not be null with extremely long phases");

            if (expectedCycles > 0) {
                assertTrue(results.size() == expectedCycles, 
                        String.format("Should have exactly %d cycles for scenario %s, got %d", 
                                expectedCycles, scenarioName, results.size()));
            }

            // Validate that long phases don't cause overflow or other issues
            for (CycleResult cycle : results) {
                assertTrue(cycle.getPhaseLength() < Double.MAX_VALUE, "Phase length should not overflow");
                assertTrue(Double.isFinite(cycle.getPhaseLength()), "Phase length should be finite");
                
                if (cycle.getPhaseLength() > 0) {
                    assertTrue(cycle.getPhaseLength() >= minExpectedDuration, 
                            String.format("Phase length %.2f should be at least %.2f for scenario %s", 
                                    cycle.getPhaseLength(), minExpectedDuration, scenarioName));
                    assertTrue(cycle.getPhaseLength() <= maxExpectedDuration, 
                            String.format("Phase length %.2f should be at most %.2f for scenario %s", 
                                    cycle.getPhaseLength(), maxExpectedDuration, scenarioName));
                }
            }

            log.info("✓ Extremely long phases test passed for scenario: {}", scenarioName);
        }

        @ParameterizedTest
        @CsvFileSource(resources = "/test-data/boundary-conditions/precision-timing-test-cases.csv", numLinesToSkip = 1)
        @DisplayName("Should handle microsecond precision timing correctly")
        void shouldHandleMicrosecondPrecisionTimingCorrectly(String scenarioName, int expectedCycles, 
                double minPrecision, boolean requiresSubMillisecondPrecision) throws IOException {
            log.info("Testing microsecond precision timing scenario: {}", scenarioName);

            // Given
            List<PerfLogEventVO> events = loadTestDataFromCsv(scenarioName);
            log.info("✓ Loaded {} events for microsecond precision scenario: {}", events.size(), scenarioName);

            // When
            List<CycleResult> results = CyclesFactory.calculateCycle(events,
                    events.get(events.size() - 1).getDateTime());

            // Then
            log.info("✓ Generated {} cycle results for scenario: {}", results.size(), scenarioName);
            assertNotNull(results, "Results should not be null with microsecond precision");

            if (expectedCycles > 0) {
                assertTrue(results.size() == expectedCycles, 
                        String.format("Should have exactly %d cycles for scenario %s, got %d", 
                                expectedCycles, scenarioName, results.size()));
            }

            // Validate precision handling
            boolean foundSubMillisecondPrecision = false;
            for (CycleResult cycle : results) {
                assertTrue(cycle.getPhaseLength() >= 0, "Phase length should be non-negative");
                
                if (cycle.getPhaseLength() > 0) {
                    assertTrue(cycle.getPhaseLength() >= minPrecision, 
                            String.format("Phase length %.6f should be at least %.6f for scenario %s", 
                                    cycle.getPhaseLength(), minPrecision, scenarioName));
                    
                    // Check for sub-millisecond precision
                    double greenMs = cycle.getGreenLength() * 1000;
                    double yellowMs = cycle.getYellowLength() * 1000;
                    double redMs = cycle.getRedLength() * 1000;
                    
                    if ((greenMs % 1 != 0) || (yellowMs % 1 != 0) || (redMs % 1 != 0)) {
                        foundSubMillisecondPrecision = true;
                    }
                }
            }

            if (requiresSubMillisecondPrecision) {
                assertTrue(foundSubMillisecondPrecision, 
                        "Should find sub-millisecond precision when required for scenario: " + scenarioName);
            }

            log.info("✓ Microsecond precision timing test passed for scenario: {} (sub-ms precision: {})", 
                    scenarioName, foundSubMillisecondPrecision);
        }

    }

    @Nested
    @DisplayName("Error Handling Scenarios")
    class ErrorHandlingScenariosTests {

        @ParameterizedTest
        @CsvFileSource(resources = "/test-data/error-handling/malformed-sequences-test-cases.csv", numLinesToSkip = 1)
        @DisplayName("Should handle malformed event sequences gracefully")
        void shouldHandleMalformedEventSequencesGracefully(String scenarioName, int expectedMinCycles, 
                int expectedMaxCycles, boolean allowSequenceIssues, int maxAllowedIssues) throws IOException {
            log.info("Testing malformed event sequences scenario: {}", scenarioName);

            // Given
            List<PerfLogEventVO> events = loadTestDataFromCsv(scenarioName);
            log.info("✓ Loaded {} events for malformed sequence scenario: {}", events.size(), scenarioName);

            // When
            List<CycleResult> results = CyclesFactory.calculateCycle(events,
                    events.get(events.size() - 1).getDateTime());

            // Then
            log.info("✓ Generated {} cycle results for scenario: {}", results.size(), scenarioName);
            assertNotNull(results, "Results should not be null with malformed sequences");

            // Validate cycle count expectations
            assertTrue(results.size() >= expectedMinCycles, 
                    String.format("Should have at least %d cycles for scenario %s, got %d", 
                            expectedMinCycles, scenarioName, results.size()));
            assertTrue(results.size() <= expectedMaxCycles, 
                    String.format("Should have at most %d cycles for scenario %s, got %d", 
                            expectedMaxCycles, scenarioName, results.size()));

            // Analyze sequence validity
            int totalIssues = 0;
            for (CycleResult cycle : results) {
                List<String> issues = new ArrayList<>();

                // Check for logical sequence issues
                if (cycle.getGreenStart() != null && cycle.getYellowStart() != null &&
                        cycle.getYellowStart().isBefore(cycle.getGreenStart())) {
                    issues.add("YELLOW_BEFORE_GREEN");
                }

                if (cycle.getYellowStart() != null && cycle.getRedStart() != null &&
                        cycle.getRedStart().isBefore(cycle.getYellowStart())) {
                    issues.add("RED_BEFORE_YELLOW");
                }

                totalIssues += issues.size();
            }

            if (!allowSequenceIssues) {
                assertTrue(totalIssues == 0, "Should not have sequence issues when not allowed");
            } else {
                assertTrue(totalIssues <= maxAllowedIssues, 
                        String.format("Should not exceed %d sequence issues, found %d", maxAllowedIssues, totalIssues));
            }

            log.info("✓ Malformed sequences test passed for scenario: {} (found {} issues)", scenarioName, totalIssues);
        }

        @ParameterizedTest
        @CsvFileSource(resources = "/test-data/error-handling/missing-starts-test-cases.csv", numLinesToSkip = 1)
        @DisplayName("Should handle missing phase starts appropriately")
        void shouldHandleMissingPhaseStartsAppropriately(String scenarioName, int expectedMinCycles, 
                int expectedMaxCycles, boolean allowMissingStarts, int maxMissingStarts) throws IOException {
            log.info("Testing missing phase starts scenario: {}", scenarioName);

            // Given
            List<PerfLogEventVO> events = loadTestDataFromCsv(scenarioName);
            log.info("✓ Loaded {} events for missing phase starts scenario: {}", events.size(), scenarioName);

            // When
            List<CycleResult> results = CyclesFactory.calculateCycle(events,
                    events.get(events.size() - 1).getDateTime());

            // Then
            log.info("✓ Generated {} cycle results for scenario: {}", results.size(), scenarioName);
            assertNotNull(results, "Results should not be null with missing phase starts");

            // Validate cycle count expectations
            assertTrue(results.size() >= expectedMinCycles, 
                    String.format("Should have at least %d cycles for scenario %s, got %d", 
                            expectedMinCycles, scenarioName, results.size()));
            assertTrue(results.size() <= expectedMaxCycles, 
                    String.format("Should have at most %d cycles for scenario %s, got %d", 
                            expectedMaxCycles, scenarioName, results.size()));

            // Analyze missing starts
            int missingStartsCount = 0;
            for (CycleResult cycle : results) {
                if (cycle.getGreenStart() == null) {
                    missingStartsCount++;
                }
            }

            if (!allowMissingStarts) {
                assertTrue(missingStartsCount == 0, "Should not have missing starts when not allowed");
            } else {
                assertTrue(missingStartsCount <= maxMissingStarts, 
                        String.format("Should not exceed %d missing starts, found %d", maxMissingStarts, missingStartsCount));
            }

            log.info("✓ Missing phase starts test passed for scenario: {} (found {} missing starts)", 
                    scenarioName, missingStartsCount);
        }

        @ParameterizedTest
        @CsvFileSource(resources = "/test-data/error-handling/orphaned-ends-test-cases.csv", numLinesToSkip = 1)
        @DisplayName("Should handle orphaned phase ends correctly")
        void shouldHandleOrphanedPhaseEndsCorrectly(String scenarioName, int expectedMinCycles, 
                int expectedMaxCycles, boolean allowOrphanedEnds, int maxOrphanedEnds) throws IOException {
            log.info("Testing orphaned phase ends scenario: {}", scenarioName);

            // Given
            List<PerfLogEventVO> events = loadTestDataFromCsv(scenarioName);
            log.info("✓ Loaded {} events for orphaned phase ends scenario: {}", events.size(), scenarioName);

            // When
            List<CycleResult> results = CyclesFactory.calculateCycle(events,
                    events.get(events.size() - 1).getDateTime());

            // Then
            log.info("✓ Generated {} cycle results for scenario: {}", results.size(), scenarioName);
            assertNotNull(results, "Results should not be null with orphaned phase ends");

            // Validate cycle count expectations
            assertTrue(results.size() >= expectedMinCycles, 
                    String.format("Should have at least %d cycles for scenario %s, got %d", 
                            expectedMinCycles, scenarioName, results.size()));
            assertTrue(results.size() <= expectedMaxCycles, 
                    String.format("Should have at most %d cycles for scenario %s, got %d", 
                            expectedMaxCycles, scenarioName, results.size()));

            // Analyze orphaned ends
            int orphanedEndsCount = 0;
            for (CycleResult cycle : results) {
                // Count cycles where we have ends without corresponding starts
                if ((cycle.getGreenEnd() != null && cycle.getGreenStart() == null) ||
                    (cycle.getYellowEnd() != null && cycle.getYellowStart() == null) ||
                    (cycle.getRedEnd() != null && cycle.getRedStart() == null)) {
                    orphanedEndsCount++;
                }
            }

            if (!allowOrphanedEnds) {
                assertTrue(orphanedEndsCount == 0, "Should not have orphaned ends when not allowed");
            } else {
                assertTrue(orphanedEndsCount <= maxOrphanedEnds, 
                        String.format("Should not exceed %d orphaned ends, found %d", maxOrphanedEnds, orphanedEndsCount));
            }

            log.info("✓ Orphaned phase ends test passed for scenario: {} (found {} orphaned ends)", 
                    scenarioName, orphanedEndsCount);
        }

    }

    @Nested
    @DisplayName("Additional Complex Scenarios")
    class AdditionalComplexScenariosTests {

        @ParameterizedTest
        @CsvFileSource(resources = "/test-data/complex-scenarios/rapid-changes-test-cases.csv", numLinesToSkip = 1)
        @DisplayName("Should handle rapid phase changes correctly")
        void shouldHandleRapidPhaseChangesCorrectly(String scenarioName, int expectedCycles, 
                double maxRapidDuration, double minAllowedDuration) throws IOException {
            log.info("Testing rapid phase changes scenario: {}", scenarioName);

            // Given
            List<PerfLogEventVO> events = loadTestDataFromCsv(scenarioName);
            log.info("✓ Loaded {} events for rapid phase changes scenario: {}", events.size(), scenarioName);

            // When
            List<CycleResult> results = CyclesFactory.calculateCycle(events,
                    events.get(events.size() - 1).getDateTime());

            // Then
            log.info("✓ Generated {} cycle results for scenario: {}", results.size(), scenarioName);
            assertNotNull(results, "Results should not be null with rapid phase changes");

            if (expectedCycles > 0) {
                assertTrue(results.size() == expectedCycles, 
                        String.format("Should have exactly %d cycles for scenario %s, got %d", 
                                expectedCycles, scenarioName, results.size()));
            }

            // Validate rapid change handling
            int rapidChangesCount = 0;
            for (CycleResult cycle : results) {
                assertTrue(cycle.getPhaseLength() >= 0, "Phase length should be non-negative");
                
                if (cycle.getPhaseLength() > 0) {
                    assertTrue(cycle.getPhaseLength() >= minAllowedDuration, 
                            String.format("Phase length %.3f should be at least %.3f for scenario %s", 
                                    cycle.getPhaseLength(), minAllowedDuration, scenarioName));
                    
                    if (cycle.getPhaseLength() <= maxRapidDuration) {
                        rapidChangesCount++;
                    }
                }
            }

            log.info("✓ Rapid phase changes test passed for scenario: {} (found {} rapid changes)", 
                    scenarioName, rapidChangesCount);
        }

        @ParameterizedTest
        @CsvFileSource(resources = "/test-data/complex-scenarios/concurrent-phases-test-cases.csv", numLinesToSkip = 1)
        @DisplayName("Should handle concurrent multi-phase operations correctly")
        void shouldHandleConcurrentMultiPhaseOperationsCorrectly(String scenarioName, int expectedMinCycles, 
                int expectedMaxCycles, boolean expectConcurrency, int maxConcurrentPhases) throws IOException {
            log.info("Testing concurrent multi-phase operations scenario: {}", scenarioName);

            // Given
            List<PerfLogEventVO> events = loadTestDataFromCsv(scenarioName);
            log.info("✓ Loaded {} events for concurrent multi-phase scenario: {}", events.size(), scenarioName);

            // When
            List<CycleResult> results = CyclesFactory.calculateCycle(events,
                    events.get(events.size() - 1).getDateTime());

            // Then
            log.info("✓ Generated {} cycle results for scenario: {}", results.size(), scenarioName);
            assertNotNull(results, "Results should not be null with concurrent phases");

            // Validate cycle count expectations
            assertTrue(results.size() >= expectedMinCycles, 
                    String.format("Should have at least %d cycles for scenario %s, got %d", 
                            expectedMinCycles, scenarioName, results.size()));
            assertTrue(results.size() <= expectedMaxCycles, 
                    String.format("Should have at most %d cycles for scenario %s, got %d", 
                            expectedMaxCycles, scenarioName, results.size()));

            // Analyze concurrent phases
            int maxConcurrentFound = 0;
            for (int i = 0; i < results.size(); i++) {
                CycleResult cycle = results.get(i);
                List<Long> concurrentPhases = new ArrayList<>();

                // Check for overlaps with other phases
                for (int j = 0; j < results.size(); j++) {
                    if (i != j) {
                        CycleResult other = results.get(j);
                        if (phasesOverlap(cycle, other)) {
                            concurrentPhases.add(other.getPhase());
                        }
                    }
                }

                maxConcurrentFound = Math.max(maxConcurrentFound, concurrentPhases.size());
            }

            if (expectConcurrency) {
                assertTrue(maxConcurrentFound > 0, "Should detect concurrent phases when expected");
            }
            assertTrue(maxConcurrentFound <= maxConcurrentPhases, 
                    String.format("Should not exceed %d concurrent phases, found %d", maxConcurrentPhases, maxConcurrentFound));

            log.info("✓ Concurrent multi-phase operations test passed for scenario: {} (max concurrent: {})", 
                    scenarioName, maxConcurrentFound);
        }

        @ParameterizedTest
        @CsvFileSource(resources = "/test-data/complex-scenarios/midnight-crossing-test-cases.csv", numLinesToSkip = 1)
        @DisplayName("Should handle boundary midnight crossing correctly")
        void shouldHandleBoundaryMidnightCrossingCorrectly(String scenarioName, int expectedCycles, 
                boolean expectMidnightCrossing, double maxDurationHours) throws IOException {
            log.info("Testing boundary midnight crossing scenario: {}", scenarioName);

            // Given
            List<PerfLogEventVO> events = loadTestDataFromCsv(scenarioName);
            log.info("✓ Loaded {} events for midnight crossing scenario: {}", events.size(), scenarioName);

            // When
            List<CycleResult> results = CyclesFactory.calculateCycle(events,
                    events.get(events.size() - 1).getDateTime());

            // Then
            log.info("✓ Generated {} cycle results for scenario: {}", results.size(), scenarioName);
            assertNotNull(results, "Results should not be null with midnight crossing");

            if (expectedCycles > 0) {
                assertTrue(results.size() == expectedCycles, 
                        String.format("Should have exactly %d cycles for scenario %s, got %d", 
                                expectedCycles, scenarioName, results.size()));
            }

            // Analyze midnight crossing
            boolean foundMidnightCrossing = false;
            for (CycleResult cycle : results) {
                assertTrue(cycle.getPhaseLength() >= 0, "Phase length should be non-negative even across midnight");
                
                if (cycle.getPhaseLength() > 0) {
                    double maxDurationSeconds = maxDurationHours * 3600;
                    assertTrue(cycle.getPhaseLength() <= maxDurationSeconds, 
                            String.format("Cycle should not exceed %.1f hours (%.0f seconds), got %.0f", 
                                    maxDurationHours, maxDurationSeconds, cycle.getPhaseLength()));
                }

                // Check for midnight crossing
                if (cycle.getGreenStart() != null && cycle.getRedEnd() != null) {
                    String startDate = cycle.getGreenStart().toLocalDate().toString();
                    String endDate = cycle.getRedEnd().toLocalDate().toString();
                    if (!startDate.equals(endDate)) {
                        foundMidnightCrossing = true;
                    }
                }
            }

            if (expectMidnightCrossing) {
                assertTrue(foundMidnightCrossing, "Should detect midnight crossing when expected");
            }

            log.info("✓ Boundary midnight crossing test passed for scenario: {} (midnight crossing: {})", 
                    scenarioName, foundMidnightCrossing);
        }

        /**
         * Helper method to determine if two phases overlap in time.
         *
         * @param phase1 First phase cycle result
         * @param phase2 Second phase cycle result
         * @return true if phases overlap, false otherwise
         */
        private boolean phasesOverlap(CycleResult phase1, CycleResult phase2) {
            if (phase1.getGreenStart() == null || phase1.getRedEnd() == null ||
                    phase2.getGreenStart() == null || phase2.getRedEnd() == null) {
                return false;
            }

            // Check if phase1 starts before phase2 ends and phase2 starts before phase1 ends
            return phase1.getGreenStart().isBefore(phase2.getRedEnd()) &&
                    phase2.getGreenStart().isBefore(phase1.getRedEnd());
        }

    }

    @Nested
    @DisplayName("Input Validation Tests")
    class InputValidationTests {

        @ParameterizedTest
        @CsvFileSource(resources = "/test-data/input-validation-test-cases.csv", numLinesToSkip = 1)
        @DisplayName("Should validate input parameters correctly")
        void shouldValidateInputParametersCorrectly(String testName, int eventsCount, boolean hasNullEvents,
                boolean hasNullDateTime, boolean hasNullEndTime, boolean expectException, String expectedExceptionType) {
            log.info("Testing input validation for: {}", testName);

            // Given
            List<PerfLogEventVO> events = null;
            LocalDateTime endTime = null;

            try {
                if (hasNullEvents) {
                    events = null;
                } else {
                    events = generateTestEvents(eventsCount, hasNullDateTime);
                }

                if (!hasNullEndTime) {
                    endTime = LocalDateTime.now().plusHours(1);
                }

                // When
                List<CycleResult> results = CyclesFactory.calculateCycle(events, endTime);

                // Then
                if (expectException) {
                    assertTrue(false, "Expected exception " + expectedExceptionType + " but none was thrown for test: " + testName);
                } else {
                    assertNotNull(results, "Results should not be null for valid input: " + testName);
                    log.info("✓ Input validation passed for test: {} (results: {})", testName, results.size());
                }

            } catch (Exception e) {
                if (expectException) {
                    assertTrue(e.getClass().getSimpleName().equals(expectedExceptionType),
                            String.format("Expected %s but got %s for test %s",
                                    expectedExceptionType, e.getClass().getSimpleName(), testName));
                    log.info("✓ Expected exception {} correctly thrown for test: {}", expectedExceptionType, testName);
                } else {
                    throw new AssertionError("Unexpected exception for test " + testName + ": " + e.getMessage(), e);
                }
            }
        }

        /**
         * Generates test events for input validation testing.
         *
         * @param count the number of events to generate
         * @param includeNullDateTime whether to include events with null datetime
         * @return list of test events
         */
        private List<PerfLogEventVO> generateTestEvents(int count, boolean includeNullDateTime) {
            List<PerfLogEventVO> events = new ArrayList<>();
            LocalDateTime baseTime = LocalDateTime.of(2025, 2, 14, 10, 0, 0);

            for (int i = 0; i < count; i++) {
                PerfLogEventVO event = new PerfLogEventVO();

                if (includeNullDateTime && i == count / 2) {
                    event.setDateTime(null); // Include one null datetime for testing
                } else {
                    event.setDateTime(baseTime.plusSeconds(i * 10));
                }

                // Cycle through different event types
                PerfLogEventVO.Event[] eventTypes = {
                    PerfLogEventVO.Event.PHASE_BEGIN_GREEN,
                    PerfLogEventVO.Event.PHASE_GREEN_TERMINATION,
                    PerfLogEventVO.Event.PHASE_BEGIN_YELLOW_CLEARANCE,
                    PerfLogEventVO.Event.PHASE_END_YELLOW_CLEARANCE,
                    PerfLogEventVO.Event.PHASE_BEGIN_RED_CLEARANCE
                };

                event.setEvent(eventTypes[i % eventTypes.length]);
                event.setParameter((i % 4) + 1); // Phases 1-4
                event.setOrdinal(i);

                events.add(event);
            }

            return events;
        }
    }

    @Nested
    @DisplayName("Performance Tests")
    class PerformanceTests {

        @ParameterizedTest
        @CsvFileSource(resources = "/test-data/performance-test-cases.csv", numLinesToSkip = 1)
        @DisplayName("Should meet performance requirements for various dataset sizes")
        void shouldMeetPerformanceRequirementsForVariousDatasetSizes(String testName, int eventCount,
                int phaseCount, int cycleCount, long maxExecutionTimeMs, int expectedResultCount) throws IOException {
            log.info("Testing performance for: {} ({} events, {} phases, {} cycles)",
                    testName, eventCount, phaseCount, cycleCount);

            // Given
            List<PerfLogEventVO> events = generatePerformanceTestEvents(eventCount, phaseCount, cycleCount);
            LocalDateTime endTime = events.get(events.size() - 1).getDateTime().plusMinutes(1);

            // When
            long startTime = System.currentTimeMillis();
            List<CycleResult> results = CyclesFactory.calculateCycle(events, endTime);
            long executionTime = System.currentTimeMillis() - startTime;

            // Then
            assertNotNull(results, "Results should not be null");
            assertTrue(executionTime <= maxExecutionTimeMs,
                    String.format("Execution time %dms should be <= %dms for test %s",
                            executionTime, maxExecutionTimeMs, testName));

            // Validate result count is reasonable (allow some variance)
            int minExpected = (int) (expectedResultCount * 0.5); // Allow 50% variance
            int maxExpected = (int) (expectedResultCount * 1.5);
            assertTrue(results.size() >= minExpected && results.size() <= maxExpected,
                    String.format("Result count %d should be between %d and %d for test %s",
                            results.size(), minExpected, maxExpected, testName));

            log.info("✓ Performance test passed for {}: {}ms execution time, {} results",
                    testName, executionTime, results.size());
        }

        /**
         * Generates test events for performance testing.
         *
         * @param eventCount total number of events to generate
         * @param phaseCount number of different phases
         * @param cycleCount approximate number of cycles
         * @return list of performance test events
         */
        private List<PerfLogEventVO> generatePerformanceTestEvents(int eventCount, int phaseCount, int cycleCount) {
            List<PerfLogEventVO> events = new ArrayList<>();
            LocalDateTime baseTime = LocalDateTime.of(2025, 2, 14, 8, 0, 0);

            int eventsPerCycle = Math.max(5, eventCount / cycleCount); // At least 5 events per cycle
            int actualCycles = eventCount / eventsPerCycle;

            for (int cycle = 0; cycle < actualCycles; cycle++) {
                for (int phase = 1; phase <= phaseCount; phase++) {
                    LocalDateTime cycleStart = baseTime.plusSeconds(cycle * 120L + phase * 10L);

                    // Generate cycle events for this phase
                    events.add(createEvent(PerfLogEventVO.Event.PHASE_BEGIN_GREEN, phase,
                            cycleStart, events.size()));
                    events.add(createEvent(PerfLogEventVO.Event.PHASE_GREEN_TERMINATION, phase,
                            cycleStart.plusSeconds(30), events.size()));
                    events.add(createEvent(PerfLogEventVO.Event.PHASE_BEGIN_YELLOW_CLEARANCE, phase,
                            cycleStart.plusSeconds(34), events.size()));
                    events.add(createEvent(PerfLogEventVO.Event.PHASE_END_YELLOW_CLEARANCE, phase,
                            cycleStart.plusSeconds(38), events.size()));
                    events.add(createEvent(PerfLogEventVO.Event.PHASE_BEGIN_RED_CLEARANCE, phase,
                            cycleStart.plusSeconds(42), events.size()));

                    if (events.size() >= eventCount) {
                        break;
                    }
                }
                if (events.size() >= eventCount) {
                    break;
                }
            }

            // Trim to exact count if needed
            if (events.size() > eventCount) {
                events = events.subList(0, eventCount);
            }

            return events.stream().sorted().collect(Collectors.toList());
        }

        /**
         * Creates a single performance test event.
         */
        private PerfLogEventVO createEvent(PerfLogEventVO.Event eventType, int phase,
                LocalDateTime dateTime, int ordinal) {
            PerfLogEventVO event = new PerfLogEventVO();
            event.setEvent(eventType);
            event.setParameter(phase);
            event.setDateTime(dateTime);
            event.setOrdinal(ordinal);
            return event;
        }
    }

}
