package com.siemens.spm.analysis.algorithm;

import lombok.Builder;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;

@Slf4j
public final class QueueLengthAlgorithm {

    private QueueLengthAlgorithm() {
    }

    private static final double STATIONARY_BORDER = 0.8;

    /**
     * N(QE) = N(GE(alpha)) + beta * q * R
     *
     * @param param {@link QueueLengthParam} phase to calculate queue length
     * @return
     */
    public static double wuFormula(QueueLengthParam param) {
        double q = param.trafficFlow;

        if (q == 0 || Double.isNaN(q) || param.cycleTime.isZero() || param.greenTime.isZero()) {
            return 0.0;
        }

        double s = param.saturationTrafficFlow;
        double queueLengthAtGE;
        double sDegree = saturationDegree(param);
        if (sDegree < STATIONARY_BORDER) {
            queueLengthAtGE = queueLengthAtGEInStationary(param);
        } else {
            queueLengthAtGE = queueLengthAtGEInNonStationary(param);
        }
        double queueLength = queueLengthAtGE + 0.9 / (1 - q / s) * q * param.redTime.toSeconds();
        if (queueLength < 0) {
            log.warn("Queue length is negative, queueLength: {}", queueLength);
            queueLength = 0;
        }
        return queueLength;
    }

    private static double queueLengthAtGEInNonStationary(QueueLengthParam param) {
        double s = param.saturationTrafficFlow;
        long cycleTime = param.cycleTime.toSeconds();
        long greenTime = param.greenTime.toSeconds();

        double sDegree = saturationDegree(param);
        double Q = s * greenTime / cycleTime;
        double T = 60;

        double factor = bunchingFactor(param.trafficFlow, sDegree, param.numOfLanes);

        double inSquare = (sDegree - 1) * (sDegree - 1)
                + factor * 8 * sDegree / (Q * T) * 2 / Math.sqrt(s * greenTime);
        return Q * T / 4 * (sDegree - 1 + Math.sqrt(inSquare));
    }

    private static double queueLengthAtGEInStationary(QueueLengthParam param) {
        double s = param.saturationTrafficFlow;
        long greenTime = param.greenTime.toSeconds();
        double sDegree = saturationDegree(param);

        double factor = bunchingFactor(param.trafficFlow, sDegree, param.numOfLanes);
        double numerator = Math.exp(-1.33 * Math.sqrt(s * greenTime) * (1 - sDegree) / sDegree);
        double denominator = 2 * (1 - sDegree);

        return factor * numerator / denominator;
    }

    public static double saturationDegree(QueueLengthParam param) {
        double q = param.trafficFlow;
        double s = param.saturationTrafficFlow;
        double g = param.greenTime.toSeconds();
        double c = param.cycleTime.toSeconds();

        return q / s * c / g;
    }

    /**
     * Calculate factor of bunching effect. If multiple lane, then factor = 1. Otherwise, calculate follow this formula
     * <p>
     * {@code 1 - (3.2 * q - 3 * q^2) / (2 -x)}
     * <p>
     * q is traffic flow
     * <p>
     * x is saturation degree
     *
     * @param trafficFlow      traffic flow
     * @param saturationDegree saturation degree
     * @param numOfLanes       number of lanes
     * @return factor of bunching effect depend on lane number
     */
    private static double bunchingFactor(double trafficFlow, double saturationDegree, int numOfLanes) {
        if (numOfLanes != 1) {
            return 1;
        } else {
            return 1 - (3.2 * trafficFlow - 3 * trafficFlow * trafficFlow) / (2 - saturationDegree);
        }
    }

    @Builder
    @Getter
    @ToString
    public static class QueueLengthParam {

        private final double trafficFlow;

        private final double saturationTrafficFlow;

        private final Duration greenTime;

        private final Duration redTime;

        private final Duration cycleTime;

        private final int numOfLanes;

    }

}
