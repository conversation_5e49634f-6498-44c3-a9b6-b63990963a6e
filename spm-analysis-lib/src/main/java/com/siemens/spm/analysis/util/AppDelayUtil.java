package com.siemens.spm.analysis.util;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.net.URL;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import org.springframework.data.util.Pair;

import com.siemens.spm.analysis.domain.Phase;
import com.siemens.spm.analysis.vo.AppDelayVehicleVO;
import com.siemens.spm.common.util.ListUtil;
import com.siemens.spm.perflog.vo.ApproachVO;
import com.siemens.spm.perflog.vo.DetectorVO;
import com.siemens.spm.perflog.vo.IntersectionConfigVO;
import com.siemens.spm.perflog.vo.LaneVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;
import com.siemens.spm.perflog.vo.PhaseVO;
import lombok.extern.slf4j.Slf4j;

import static com.siemens.spm.analysis.util.IntersectionConfigUtils.validatePhase;

/**
 * Utility functions for approach delay calculation
 *
 * <AUTHOR> (<EMAIL>)
 */
@Slf4j
public class AppDelayUtil {

    // hide public ctor
    private AppDelayUtil() {
    }

    /**
     * Calculation of the approach delay using the webster formula
     * <p>
     * Formula can be found in EGS Sharepoint: PROJECT SIWAVE Insights - Estimation Waiting
     * Times\Papers\TMC1_TrafficSignalControl.pdf:70
     *
     * @param vehicleVolume    vehicle count in interval
     * @param intervalDuration interval duration
     * @param greenDuration    green duration in interval
     * @return estimated approach delay
     */
    public static AppDelayEstimationResult websterAppDelayEstimation(int vehicleVolume,
                                                                     double intervalDuration,
                                                                     double greenDuration,
                                                                     double oversaturationBorder,
                                                                     double saturationFlow) {

        // check if there are any vehicles in the bin and the bin has a size
        if (!(vehicleVolume > 0 && intervalDuration > 0 && greenDuration > 0)) {

            // if not there is no delay
            return new AppDelayEstimationResult(0.0, false);

        } else {

            double greenShare = greenDuration / intervalDuration; // [-]

            // if there are vehicles, use webster formula to estimate delay
            double flow = vehicleVolume / intervalDuration; // [veh/s]

            // saturation flow is dependant on the approach topology.
            double saturationDegree = Math.min(flow / (greenShare * saturationFlow), 0.99); // [-]

            // calculate parts of the formula
            double a = Math.pow(1 - greenShare, 2) / (2 * (1 - greenShare * saturationDegree));
            double b = Math.pow(saturationDegree, 2) / (2 * (1 - saturationDegree));
            double c = 0.65 * Math.pow(intervalDuration / Math.pow(flow, 2), 1 / 3.0)
                    * Math.pow(saturationDegree, 2 + 5 * greenShare);

            // combine parts of the formula to get average delay
            double d = intervalDuration * a + b / flow - c;

            // check if approach is oversaturated
            boolean oversaturated = saturationDegree >= oversaturationBorder;

            return new AppDelayEstimationResult(d, oversaturated);
        }
    }

    /**
     * Identifies phaseVO depending on given phase from the configuration
     *
     * @param intConfigVO configuration to search through
     * @param phase       phase of the approach looked for
     * @return {@link ApproachVO} if found in configuration, null otherwise
     */
    public static ApproachVO identifyApproach(IntersectionConfigVO intConfigVO,
                                              Phase phase) {
        if (intConfigVO == null) {
            return null;
        }

        List<ApproachVO> approaches = intConfigVO.getApproaches();
        if (approaches == null || approaches.isEmpty()) {
            return null;
        }

        for (ApproachVO approach : approaches) {
            Double approachSpeed = approach.getApproachSpeedMetersPerSecond();
            List<PhaseVO> phases = approach.getPhases();
            if (approachSpeed == null || approachSpeed <= 0 || phases == null) {
                continue;
            }
            for (PhaseVO phaseVO : phases) {
                if (validatePhase(phase.getPhaseNum(), phaseVO)) {
                    return approach;
                }
            }
        }

        return null;
    }

    /**
     * Identifies lanes using phase
     *
     * @param approachVO approach containing lanes
     * @param phase      phase for lanes to find
     * @param laneIdx    number of lane to identify
     * @return found lane. null if none are found.
     */
    public static LaneVO identifyLane(ApproachVO approachVO, Phase phase, int laneIdx) {

        if (approachVO == null) {
            return null;
        }

        for (PhaseVO phaseVO :
                approachVO.getPhases()) {
            if (validatePhase(phase.getPhaseNum(), phaseVO)) {
                return phaseVO.getLanes().get(laneIdx);
            }
        }

        return null;
    }

    /**
     * Gets detector position from approach and detector id
     *
     * @param approachVO approach to look for the detector
     * @param detectorId detector id to look for
     * @return position of detector [m]
     */
    public static double getDetectorPosition(ApproachVO approachVO, int detectorId) {

        if (approachVO == null) {
            return Double.NaN;
        }

        for (PhaseVO phase :
                approachVO.getPhases()) {

            for (LaneVO lane :
                    phase.getLanes()) {

                for (DetectorVO detector :
                        lane.getDetectors()) {

                    if (detector.getDetectorNumber() == detectorId) {
                        return detector.getDistanceInMeters();
                    }
                }
            }
        }

        return Double.NaN;
    }

    /**
     * Reads Perflog events from file in resources folder. File needs to be in format: TIME;EVENT_ID;EVENT_PARAMETER
     *
     * @param T0          Start time. TIME from the file will be added to this to get a {@link LocalDateTime}
     * @param path        Path of result file in resources folder
     * @param classLoader Necessary to access resources
     * @return List of Perflog events
     */
    public static List<PerfLogEventVO> readPerflogEventsFromResourcesFile(LocalDateTime T0,
                                                                          String path,
                                                                          ClassLoader classLoader) {
        List<PerfLogEventVO> events = new ArrayList<>();

        URL url = classLoader.getResource(path);
        if (url == null) {
            log.debug("Resource file for test not found.");
            return new ArrayList<>();
        }

        File file = new File(url.getFile());

        try (BufferedReader br = new BufferedReader(new FileReader(file.getPath()))) {
            String line;
            while ((line = br.readLine()) != null) {
                String[] data = line.split(";");

                double timeInSeconds = Double.parseDouble(data[0]);
                int eventId = Integer.parseInt(data[1]);
                int eventParameter = Integer.parseInt(data[2]);

                LocalDateTime eventTime = T0.plusSeconds((int) timeInSeconds);

                PerfLogEventVO event = new PerfLogEventVO(eventTime, PerfLogEventVO.Event.of(eventId), eventParameter);
                events.add(event);
            }
        } catch (IOException e) {
            log.error("Test Perflog events file could not be opened.");
        }

        return events;
    }

    /**
     * Reads simulation bin results from file in resources folder. File needs to be in format:
     * BIN_START;BIN_END;BIN_AVG_DELAY;BIN_VOLUME
     *
     * @param T0              Start time. BIN_START and BIN_END from the file will be added to this to get a {@link
     *                        LocalDateTime}
     * @param path            Path of result file in resources folder
     * @param classLoader     Necessary to access resources
     * @param useAverageDelay if true average delay of multiple simulation runs will be used, if false delay from the
     *                        simulation run the perflog events are created from are used
     * @return List of delays per bin
     */
    public static List<AppDelayVehicleVO> readResultsDelaysFromResourcesFile(LocalDateTime T0,
                                                                             String path,
                                                                             ClassLoader classLoader,
                                                                             boolean useAverageDelay) {
        List<AppDelayVehicleVO> delays = new ArrayList<>();

        URL url = classLoader.getResource(path);
        if (url == null) {
            log.debug("Resource file for test not found.");
            return new ArrayList<>();
        }

        File file = new File(url.getFile());

        try (BufferedReader br = new BufferedReader(new FileReader(file.getPath()))) {
            String line;
            while ((line = br.readLine()) != null) {
                String[] data = line.split(";");

                int start = Integer.parseInt(data[0]);
                int end = Integer.parseInt(data[1]);
                double delay;
                if (useAverageDelay) {
                    delay = Double.parseDouble(data[2]);
                } else {
                    delay = Double.parseDouble(data[3]);
                }
                int vehicles = Integer.parseInt(data[4]);

                AppDelayVehicleVO binDelay = new AppDelayVehicleVO(T0.plusSeconds(start), T0.plusSeconds(end),
                        delay * vehicles, vehicles);
                delays.add(binDelay);
            }
        } catch (IOException e) {
            log.error("Test results file could not be opened.");
        }

        return delays;
    }

    /**
     * Identifies phaseVO depending on given phase from the configuration
     *
     * @param intConfigVO configuration to search through
     * @param phaseNum    phase of the approach looked for
     * @return PhaseVO and approach speed (mph) if found in configuration, null otherwise
     */
    public static Pair<PhaseVO, Double> identifyPhase(IntersectionConfigVO intConfigVO,
                                        int phaseNum) {

        if (intConfigVO == null || ListUtil.hasNoItem(intConfigVO.getApproaches())) {
            return null;
        }

        for (ApproachVO approach : intConfigVO.getApproaches()) {
            Double approachSpeed = approach.getApproachSpeed();
            List<PhaseVO> phases = approach.getPhases();
            if (approachSpeed == null || approachSpeed <= 0 || phases == null) {
                continue;
            }
            for (PhaseVO phaseVO : phases) {
                if (validatePhase(phaseNum, phaseVO)) {
                    return Pair.of(phaseVO, approachSpeed);
                }
            }
        }

        return null;
    }

}
