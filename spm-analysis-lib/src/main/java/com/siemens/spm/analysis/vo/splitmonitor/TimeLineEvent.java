package com.siemens.spm.analysis.vo.splitmonitor;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.perflog.vo.PerfLogEventVO;
import lombok.Builder;
import lombok.Data;
import lombok.NonNull;

import java.time.LocalDateTime;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
public class TimeLineEvent implements Comparable<TimeLineEvent> {

    @JsonProperty("event") private String event;

    @JsonProperty("time") private LocalDateTime time;

    @JsonProperty("phase")
    private Long param;

    @Override
    public int compareTo(@NonNull TimeLineEvent o) {
        int compare = this.time.compareTo(o.getTime());
        if (compare == 0) {
            return param.compareTo(o.getParam());
        }
        return compare;
    }

    enum Event {
        GAP_OUT, MAX_OUT, FORCE_OFF, PATTERN_CHANGE;

        public String name(int phase) {
            return super.name().replace("X", String.valueOf(phase));
        }

        public static String from(PerfLogEventVO.Event perfLogEvent) {
            switch (perfLogEvent) {
            case PHASE_GAP_OUT:
                return GAP_OUT.name();
            case PHASE_MAX_OUT:
                return MAX_OUT.name();
            case PHASE_FORCE_OFF:
                return FORCE_OFF.name();
            case COORD_PATTERN_CHANGE:
                return PATTERN_CHANGE.name();
            default:
                return "-";
            }
        }
    }
}
