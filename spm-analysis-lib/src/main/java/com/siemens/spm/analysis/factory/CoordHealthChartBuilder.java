package com.siemens.spm.analysis.factory;

import com.siemens.spm.analysis.aggregator.CoordinationHealthAggregator;
import com.siemens.spm.analysis.util.DateTimeUtil;
import com.siemens.spm.analysis.vo.CoordinationHealthBinVO;
import com.siemens.spm.analysis.vo.CoordinationHealthChartVO;
import com.siemens.spm.analysis.vo.CoordinationHealthPlanStatisticsVO;
import com.siemens.spm.analysis.vo.Plan;
import com.siemens.spm.perflog.vo.IntersectionConfigVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class CoordHealthChartBuilder {

    private CoordinationHealthChartVO healthChartVO;

    private CoordinationHealthPlanStatisticsVO currentPlanStat;

    private CoordinationHealthAggregator healthAggregator;

    private List<CoordinationHealthPlanStatisticsVO> planStats;

    private static final int COORD_HEALTH_INSTEP = 1;
    private static final int COORD_HEALTH_TRANS_2 = 2;
    private static final int COORD_HEALTH_TRANS_3 = 3;
    private static final int COORD_HEALTH_TRANS_4 = 4;

    private int instep;
    private int transition;

    /**
     * @param chartVO Chart object to be build, must not be null
     * @param binSize Volume bin size in seconds from {@link CoordinationHealthBinVO }
     */
    public CoordHealthChartBuilder(CoordinationHealthChartVO chartVO, int binSize) {
        init(chartVO, binSize);
    }

    private void init(CoordinationHealthChartVO chartVO, int binSize) {
        this.healthChartVO = chartVO;
        this.healthAggregator = new CoordinationHealthAggregator(chartVO.getFromTime(), binSize);
        this.currentPlanStat = new CoordinationHealthPlanStatisticsVO(Plan.UNKNOWN_PLAN, chartVO.getFromTime());
        this.instep = 0;
        this.transition = 0;
        this.planStats = new ArrayList<>();
    }

    public void putEvent(PerfLogEventVO eventVO) {
        // add event to health bin aggregator for process Bin data
        healthAggregator.putEvent(eventVO);

        if (!DateTimeUtil.eventDateTimeGuard(eventVO, healthChartVO.getFromTime())) {
            return; // Skip event with invalid datetime
        }

        switch (eventVO.getEvent()) {
        case COORD_PATTERN_CHANGE: // 131
            // add new plan with plan name = evenVO.getParameter()
            putCoorHealthPatternChange(eventVO);
            break;
        case COORD_CYCLE_STATE_CHANGE:
            // with each even COORD_CYCLE_STATE_CHANGE, phase instep, cycle or transition in
            // plan increase by 1
            if ((int) eventVO.getParameter() == COORD_HEALTH_INSTEP) {
                instep++;
            } else if ((int) eventVO.getParameter() == COORD_HEALTH_TRANS_2
                    || (int) eventVO.getParameter() == COORD_HEALTH_TRANS_3
                    || (int) eventVO.getParameter() == COORD_HEALTH_TRANS_4) {
                transition++;
            }

            break;

        default:
            break;
        }
    }

    public void build() {
        healthAggregator.fillBins(healthChartVO.getToTime());
        healthChartVO.setCoorHealthBinList(healthAggregator.getBinList());
        // close plan with to_time
        closeCurrentPlanStat(healthChartVO.getToTime());
        for (CoordinationHealthPlanStatisticsVO planStatisticsVO : planStats) {
            healthChartVO.addCoorHealthPlanStatitics(planStatisticsVO);
        }
    }

    private void closeCurrentPlanStat(LocalDateTime toTime) {
        currentPlanStat.setToTime(toTime);
        planStats.add(currentPlanStat);

        currentPlanStat.addInStep(instep);
        currentPlanStat.addTransition(transition);

        // Clear current count variable
        instep = 0;
        transition = 0;
        currentPlanStat = null;
    }

    private void putCoorHealthPatternChange(PerfLogEventVO eventVO) {
        closeCurrentPlanStat(eventVO.getDateTime());
        // Start new phanStatistics
        currentPlanStat = new CoordinationHealthPlanStatisticsVO((int) eventVO.getParameter(), eventVO.getDateTime());
    }

    public void setIntersectionConfig(IntersectionConfigVO configVO) {
        healthAggregator.setConfig(configVO);
    }

}
