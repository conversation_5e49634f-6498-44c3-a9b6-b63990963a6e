package com.siemens.spm.analysis.factory.splitmonitor;

import com.siemens.spm.analysis.vo.splitmonitor.CycleResult;
import com.siemens.spm.analysis.vo.splitmonitor.CyclesFactory;
import com.siemens.spm.analysis.vo.splitmonitor.PlanBoundary;
import com.siemens.spm.analysis.vo.splitmonitor.SplitMonitorChartVO;
import com.siemens.spm.perflog.vo.IntersectionConfigVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Admin
 * @version : 1.0
 * @since : 3/7/2025
 **/
public class SplitMonitorChartBuilderV2 {

    private IntersectionConfigVO intConfigVO;

    private Map<PlanBoundary, List<CycleResult>> planCycles;

    public SplitMonitorChartVO build(List<PerfLogEventVO> events, LocalDateTime fromTime, LocalDateTime toTime) {
        List<CycleResult> cycles = CyclesFactory.calculateCycle(events, toTime);
        return null;
    }

}
