package com.siemens.spm.analysis.vo.abnormaldata;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.concurrent.atomic.AtomicLong;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
public class DateHoursRawCount implements Serializable {

    private static final long serialVersionUID = -7654393254390886814L;

    @JsonProperty("data")
    private LocalDate date;

    @JsonProperty("hours")
    private int hours;

    @JsonProperty("phase")
    private long param;

    @JsonProperty("raw_count")
    private final AtomicLong rawCount = new AtomicLong(0L);

    public DateHoursRawCount inc() {
        rawCount.incrementAndGet();
        return this;
    }

    public DateHoursRawCount inc(Long value) {
        rawCount.addAndGet(value);
        return this;
    }

    public String key() {
        return buildKey(this.param, this.date, this.hours);
    }

    public static final String KEY_FORMAT = "%s_%s_%s";

    public static String buildKey(long param, LocalDate date, int hours) {
        return String.format(KEY_FORMAT, param, date, hours);
    }
}
