package com.siemens.spm.analysis.factory;

import com.siemens.spm.analysis.exception.AnalysisInitializationException;
import com.siemens.spm.analysis.exception.ChartBuilderInitializationException;
import com.siemens.spm.analysis.exception.InvalidPerfLogException;
import com.siemens.spm.analysis.util.ChartType;
import com.siemens.spm.analysis.vo.VolumeAnalysisVO;
import com.siemens.spm.analysis.vo.VolumeChartVO;
import com.siemens.spm.perflog.vo.IntersectionConfigVO;
import com.siemens.spm.perflog.vo.PerfLogBundleVO;
import com.siemens.spm.perflog.vo.PerfLogChunkVO;
import com.siemens.spm.perflog.vo.PerfLogGapVO;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class VolumeAnalysisFactory
        extends ArrivalOnAnalysisFactory<VolumeAnalysisVO, VolumeChartVO, VolumeChartBuilder> {

    public VolumeAnalysisFactory(int binSize) {
        super(binSize, VolumeChartVO.class, VolumeChartBuilder.class);
    }

    public VolumeAnalysisVO createAnalysis(LocalDateTime fromTime,
                                           LocalDateTime toTime,
                                           PerfLogBundleVO perfLogBundleVO,
                                           List<PerfLogGapVO> perfLogGapVOList)
            throws InvalidPerfLogException, AnalysisInitializationException {
        return createAnalysis(fromTime, toTime,
                perfLogBundleVO, perfLogGapVOList,
                VolumeAnalysisVO.class,
                VolumeChartVO.class);
    }

    @Override
    protected void createCharts(LocalDateTime fromTime, LocalDateTime toTime, List<PerfLogChunkVO> perfLogChunkVOList,
                                Map<String, IntersectionConfigVO> intConfigVOMap, Set<Integer> phaseSet,
                                Class<VolumeChartVO> chartClass)
            throws ChartBuilderInitializationException {
        // Create chartVOMap: phase number -> chart VO
        HashMap<Integer, VolumeChartVO> chartVOMap = new HashMap<>();
        VolumeChartVO chartVO = new VolumeChartVO();
        //Set all phases chart

        chartVO.setPhase(0);
        chartVO.setFromTime(fromTime);
        chartVO.setToTime(toTime);
        chartVO.setChartType(ChartType.VOLUME_ALL_PHASE.getChartName());
        chartVOMap.put(0, chartVO);
        // Set time range to each chart
        for (Integer phaseNum : phaseSet) {
            chartVO = new VolumeChartVO();
            chartVO.setPhase(phaseNum);
            chartVO.setFromTime(fromTime);
            chartVO.setChartType(ChartType.VOLUME_ONE_PHASE.getChartName());
            chartVO.setToTime(toTime);
            chartVOMap.put(phaseNum, chartVO);
        }
        // Scan PerfLog to build other chart data
        scanPerfLog(fromTime, toTime, perfLogChunkVOList, intConfigVOMap, chartVOMap);
        for (VolumeChartVO chart : chartVOMap.values()) {
            analysisVO.addChart(chart);
        }
    }

}
