package com.siemens.spm.analysis.vo.splitmonitor;

import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Factory class for determining and restructuring ring configurations based on coordinated phases.
 * <p>
 * This utility class provides methods to analyze ring structures and reorder them so that
 * the sequence starts from the earliest coordinated phase found across all rings.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 30/6/2025
 */
public final class RingStructureFactory {

    private RingStructureFactory() {
    }

    /**
     * Determines the ring structure based on coordinated phases and reorders rings
     * to start from the earliest coordinated phase.
     * <p>
     * This method finds all coordinated phases across all rings, identifies the earliest
     * one by index position, and then rotates all rings to start from that position.
     * </p>
     *
     * @param rings the list of rings containing phases to be analyzed and reordered.
     *              Must not be null and must contain at least one ring with a coordinated phase.
     * @return a new list of rings with phases reordered to start from the earliest coordinated phase
     * @throws IllegalArgumentException if the rings list is null, empty, or contains no coordinated phases
     * @throws IllegalStateException    if any ring contains a null phases list
     */
    public static List<Ring> determineRingStructure(List<Ring> rings) {
        if (CollectionUtils.isEmpty(rings)) {
            throw new IllegalArgumentException("Rings list is invalid");
        }
        List<Phase> coordinatedPhases = findCoordinatedPhases(rings);
        int startIndex = findEarliestCoordinatedPhaseIndex(coordinatedPhases, rings);
        return createReorderedRings(rings, startIndex);
    }

    /**
     * Determines the ring structure from ring configurations and coordinated phases,
     * reordering rings to start from the earliest coordinated phase.
     * <p>
     * This method constructs two rings from the provided configurations, identifies
     * their coordinated phases, finds the earliest coordinated phase by index position,
     * and then rotates both rings to start from that position.
     * </p>
     *
     * @param ringConfigs    a 2D array containing phase configurations for two rings.
     *                       Must not be null and must contain exactly 2 ring configurations.
     *                       Each ring configuration is an array of phase numbers.
     * @param ring1CoordPhase the phase number that is coordinated in the first ring
     * @param ring2CoordPhase the phase number that is coordinated in the second ring
     * @return a new list of rings with phases reordered to start from the earliest coordinated phase
     * @throws IllegalArgumentException if no coordinated phases are found in the constructed rings
     * @throws ArrayIndexOutOfBoundsException if ringConfigs does not contain at least 2 ring configurations
     */
    public static List<Ring> determineRingStructure(int[][] ringConfigs, int ring1CoordPhase, int ring2CoordPhase) {
        List<Ring> rings = new ArrayList<>();
        int[] ring1Config = ringConfigs[0];
        int[] ring2Config = ringConfigs[1];

        Ring ring1 = buildRing(ring1Config, ring1CoordPhase);
        rings.add(ring1);
        Ring ring2 = buildRing(ring2Config, ring2CoordPhase);
        rings.add(ring2);

        List<Phase> coordinatedPhases = new ArrayList<>();
        coordinatedPhases.add(ring1.getCoordinatedPhase());
        coordinatedPhases.add(ring2.getCoordinatedPhase());

        int startIndex = findEarliestCoordinatedPhaseIndex(coordinatedPhases, rings);
        return createReorderedRings(rings, startIndex);
    }

    /**
     * Builds a ring from a phase configuration array and coordinated phase number.
     * <p>
     * This method creates a Ring object containing Phase objects constructed from
     * the provided phase numbers. The phase matching the coordinated phase number
     * will be marked as coordinated.
     * </p>
     *
     * @param ringConfig the array of phase numbers for this ring
     * @param coordPhase the phase number that should be marked as coordinated
     * @return a new Ring object containing the configured phases
     */
    private static Ring buildRing(int[] ringConfig, int coordPhase) {
        List<Phase> phases = new ArrayList<>();
        for (int phaseNum : ringConfig) {
            boolean isCoordinated = phaseNum == coordPhase;
            phases.add(new Phase(phaseNum, isCoordinated));
        }
        return new Ring(phases);
    }

    /**
     * Finds all coordinated phases across all rings.
     *
     * @param rings the list of rings to search through
     * @return a list of all coordinated phases found
     * @throws IllegalStateException if any ring has a null phases list
     */
    private static List<Phase> findCoordinatedPhases(List<Ring> rings) {
        List<Phase> coordinatedPhases = new ArrayList<>();

        for (Ring ring : rings) {
            List<Phase> phases = ring.getPhases();
            if (phases == null) {
                throw new IllegalStateException("Ring phases list cannot be null");
            }

            for (Phase phase : phases) {
                if (phase != null && phase.isCoordinated()) {
                    coordinatedPhases.add(phase);
                }
            }
        }
        if (CollectionUtils.isEmpty(coordinatedPhases)) {
            throw new IllegalArgumentException("No coordinated phases found in any ring");
        }

        return coordinatedPhases;
    }

    /**
     * Finds the index of the earliest coordinated phase across all rings.
     *
     * @param coordinatedPhases the list of coordinated phases to examine
     * @param rings             the list of rings to search within
     * @return the minimum index position of any coordinated phase
     */
    private static int findEarliestCoordinatedPhaseIndex(List<Phase> coordinatedPhases, List<Ring> rings) {
        int minIndex = Integer.MAX_VALUE;

        for (Phase coordinatedPhase : coordinatedPhases) {
            int index = findPhaseIndexInRings(coordinatedPhase, rings);
            if (index != -1 && index < minIndex) {
                minIndex = index;
            }
        }

        return minIndex;
    }

    /**
     * Finds the index of a specific phase within the rings.
     *
     * @param targetPhase the phase to find
     * @param rings       the rings to search within
     * @return the index of the phase, or -1 if not found
     */
    private static int findPhaseIndexInRings(Phase targetPhase, List<Ring> rings) {
        for (Ring ring : rings) {
            int index = ring.getPhases().indexOf(targetPhase);
            if (index != -1) {
                return index;
            }
        }
        return -1;
    }

    /**
     * Creates new rings with phases rotated to start from the specified index.
     *
     * @param originalRings the original rings to reorder
     * @param startIndex    the index position to start the rotation from
     * @return a new list of rings with rotated phase sequences
     */
    private static List<Ring> createReorderedRings(List<Ring> originalRings, int startIndex) {
        List<Ring> reorderedRings = new ArrayList<>();

        for (Ring ring : originalRings) {
            List<Phase> rotatedPhases = rotatePhases(ring.getPhases(), startIndex);
            reorderedRings.add(new Ring(rotatedPhases));
        }

        return reorderedRings;
    }

    /**
     * Rotates a list of phases to start from the specified index.
     *
     * @param originalPhases the original phase list
     * @param startIndex     the index to start rotation from
     * @return a new list with phases rotated to start from the specified index
     */
    private static List<Phase> rotatePhases(List<Phase> originalPhases, int startIndex) {
        List<Phase> rotatedPhases = new ArrayList<>();
        int phaseCount = originalPhases.size();

        for (int i = 0; i < phaseCount; i++) {
            int rotatedIndex = (startIndex + i) % phaseCount;
            rotatedPhases.add(originalPhases.get(rotatedIndex));
        }

        return rotatedPhases;
    }

}
