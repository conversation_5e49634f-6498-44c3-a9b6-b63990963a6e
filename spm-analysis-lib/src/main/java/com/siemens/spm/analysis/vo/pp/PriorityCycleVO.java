package com.siemens.spm.analysis.vo.pp;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PriorityCycleVO implements Serializable {

    private static final long serialVersionUID = -1586124601903295650L;

    @JsonProperty("tsp_check_in")
    private LocalDateTime tspCheckIn;

    @JsonProperty("adj_early_green")
    private LocalDateTime adjEarlyGreen;

    @JsonProperty("adj_extend_green")
    private LocalDateTime adjExtendGreen;

    @JsonProperty("tsp_check_out")
    private LocalDateTime tspCheckOut;

}
