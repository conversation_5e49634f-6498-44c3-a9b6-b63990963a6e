package com.siemens.spm.analysis.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.ArrayList;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CoordinationHealthChartVO extends PhaseChartVO {

    private static final long serialVersionUID = -3017262764509061275L;

    @JsonProperty("cha_bins")
    private List<CoordinationHealthBinVO> coorHealthBinList;

    @JsonProperty("plan_statistics")
    private List<CoordinationHealthPlanStatisticsVO> coorHealthPlanStatisticsList;

    @JsonProperty("phase")
    private String setPhaseAll() {
        if (getPhase() == 0) {
            return "all";
        }
        return String.valueOf(getPhase());
    }

    public void addCoorHealthPlanStatitics(CoordinationHealthPlanStatisticsVO healthPlanStatisticsVO) {
        if (healthPlanStatisticsVO == null) {
            throw new IllegalArgumentException("CoordinationHealthPlanStatisticsVO must not be null");
        }
        if (coorHealthPlanStatisticsList == null)
            coorHealthPlanStatisticsList = new ArrayList<>();

        coorHealthPlanStatisticsList.add(healthPlanStatisticsVO);
    }

    @JsonIgnore
    public int getTransitionPer() {
        int transitionHit = 0;
        for (CoordinationHealthBinVO cycleVO : coorHealthBinList) {
            if (cycleVO != null) {
                transitionHit += cycleVO.totalTransitions();
            }
        }
        return transitionHit;
    }

    @JsonIgnore
    public int getInstepPer() {
        int instepHit = 0;
        for (CoordinationHealthBinVO cycleVO : coorHealthBinList) {
            if (cycleVO != null) {
                instepHit += cycleVO.totalInsteps();
            }
        }
        return instepHit;
    }

    @JsonIgnore
    public int getCyclePer() {
        int cycleHit = 0;
        for (CoordinationHealthBinVO cycleVO : coorHealthBinList) {
            if (cycleVO != null) {
                cycleHit += cycleVO.totalCycles();
            }
        }
        return cycleHit;
    }

}
