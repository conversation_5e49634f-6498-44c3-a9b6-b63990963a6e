package com.siemens.spm.analysis.vo.splitmonitor;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR> Admin
 * @version : 1.0
 * @since : 30/6/2025
 **/
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class Ring {

    private List<Phase> phases;

    public Phase getCoordinatedPhase() {
        return phases.stream().filter(Phase::isCoordinated).findFirst().orElse(null);
    }

}