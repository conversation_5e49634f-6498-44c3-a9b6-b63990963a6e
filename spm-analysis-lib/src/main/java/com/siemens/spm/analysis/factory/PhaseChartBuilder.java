/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : PhaseChartBuilder.java
 * Project     : spm-analysis-lib
 */
package com.siemens.spm.analysis.factory;

import com.siemens.spm.analysis.domain.Phase;
import com.siemens.spm.perflog.vo.IntersectionConfigVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;

import java.time.Duration;

public abstract class PhaseChartBuilder {

    protected Phase phase;

    protected PhaseChartBuilder(int phaseNum) {
        phase = new Phase(phaseNum);
    }

    public void setPhaseApproach(Phase.Approach approach) {
        phase.setPhaseApproach(approach);
    }

    /**
     * Handle phase event and update phase state machine
     *
     * @param eventVO eventVO
     * @throws IllegalArgumentException if eventVO is null
     */
    public void putEvent(PerfLogEventVO eventVO) {
        if (eventVO == null)
            throw new IllegalArgumentException("eventVO must not be null");

        if (eventVO.isPhaseEvent()) {
            putPhaseEvent(eventVO);
        }
    }

    public int computePhaseTime(PerfLogEventVO eventVO) {
        if (eventVO == null)
            throw new IllegalArgumentException("eventVO must not be null");

        Duration duration = phase.getOnTime() != null
                ? Duration.between(phase.getOnTime(), eventVO.getDateTime())
                : Duration.ZERO;

        return (int) duration.getSeconds();
    }

    /**
     * Finalize chart building and output to referenced chartVO
     */
    public abstract void build();

    /**
     * Handle put phase event
     *
     * @param eventVO eventVO
     */
    protected void putPhaseEvent(PerfLogEventVO eventVO) {
        phase.updateState(eventVO);
    }

    /**
     * Set intersection config to use whenever needed
     *
     * @param configVO {@link IntersectionConfigVO} object pass for use
     */
    public abstract void setIntersectionConfig(IntersectionConfigVO configVO);

}
