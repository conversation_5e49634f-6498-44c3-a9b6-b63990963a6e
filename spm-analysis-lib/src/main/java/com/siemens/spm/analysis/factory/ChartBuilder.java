package com.siemens.spm.analysis.factory;

import com.siemens.spm.perflog.vo.IntersectionConfigVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;

public abstract class ChartBuilder {

    /**
     * Set intersection config to use whenever needed
     *
     * @param configVO {@link IntersectionConfigVO} object pass for use
     */
    public abstract void setIntersectionConfig(IntersectionConfigVO configVO);

    /**
     * Put event to chart builder for processing
     *
     * @param eventVO eventVO
     */
    public abstract void putEvent(PerfLogEventVO eventVO);

    /**
     * Finalize chart building and output to referenced chartVO
     */
    public abstract void build();

}
