package com.siemens.spm.analysis.vo.splitmonitor;

import com.siemens.spm.analysis.vo.Plan;
import com.siemens.spm.perflog.vo.PerfLogEventVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> Admin
 * @version : 1.0
 * @since : 3/7/2025
 **/
@Slf4j
public final class PlanBoundaryFactory {

    private PlanBoundaryFactory() {
    }

    /**
     * Creates plan boundaries based on coordination pattern change events.
     * <p>
     * Plan boundaries represent time periods where a specific coordination pattern is active.
     * Each boundary has a start time and an end time (nextPlanStart), defining when the pattern
     * was active. The method handles:
     * 1. Initial boundary from the last known pattern before the analysis period
     * 2. Boundaries from pattern change events within the analysis period
     * 3. Proper linking of boundaries with their end times
     *
     * @param lastCoordPatternChanged The last coordination pattern change before the analysis period
     * @param patternChangeEvents     Performance log data pattern change events
     * @param fromTime                Start time of the analysis period
     * @param toTime                  End time of the analysis period
     * @return List of plan boundaries ordered chronologically
     */
    public static List<PlanBoundary> getPlanBoundaries(PerfLogEventVO lastCoordPatternChanged,
                                                       List<PerfLogEventVO> patternChangeEvents,
                                                       LocalDateTime fromTime,
                                                       LocalDateTime toTime) {
        if (fromTime == null || toTime == null) {
            log.warn("Invalid time range: fromTime={}, toTime={}", fromTime, toTime);
            return new ArrayList<>();
        }

        List<PlanBoundary> planBoundaries = new ArrayList<>();

        PlanBoundary initialBoundary = createInitialPlanBoundary(lastCoordPatternChanged, fromTime);
        planBoundaries.add(initialBoundary);

        // Create plan boundaries from pattern change events within the analysis period
        List<PlanBoundary> eventBoundaries = createPlanBoundariesFromEvents(patternChangeEvents);
        planBoundaries.addAll(eventBoundaries);

        // Set the nextPlanStart times to properly link boundaries
        setNextPlanStartTimes(planBoundaries, toTime);

        return planBoundaries;
    }

    /**
     * Creates the initial plan boundary representing the coordination pattern active
     * at the start of the analysis period or unknown if not provided.
     *
     * @param lastCoordPatternChanged The last known pattern change before analysis period
     * @param fromTime                Start time of the analysis period
     * @return Initial plan boundary
     */
    private static PlanBoundary createInitialPlanBoundary(PerfLogEventVO lastCoordPatternChanged,
                                                          LocalDateTime fromTime) {
        if (lastCoordPatternChanged != null) {
            long patternParameter = lastCoordPatternChanged.getParameter();
            LocalDateTime patternStartTime = lastCoordPatternChanged.getDateTime();
            log.debug("Creating initial boundary with known pattern: {} at {}",
                    patternParameter, patternStartTime);
            return PlanBoundary.builder()
                    .plan(patternParameter)
                    .planStart(patternStartTime)
                    .build();
        } else {
            log.debug("Creating initial boundary with unknown pattern at {}", fromTime);
            return PlanBoundary.builder()
                    .plan(Plan.UNKNOWN_PLAN)
                    .planStart(fromTime)
                    .build();
        }
    }

    /**
     * Creates plan boundaries from coordination pattern change events.
     *
     * @param patternChangeEvents List of pattern change events
     * @return List of plan boundaries created from events
     */
    private static List<PlanBoundary> createPlanBoundariesFromEvents(List<PerfLogEventVO> patternChangeEvents) {
        List<PlanBoundary> eventBoundaries = new ArrayList<>();

        if (CollectionUtils.isEmpty(patternChangeEvents)) {
            log.debug("No pattern change events found");
            return eventBoundaries;
        }
        log.debug("Creating {} plan boundaries from pattern change events", patternChangeEvents.size());
        for (PerfLogEventVO event : patternChangeEvents) {
            if (event.getDateTime() == null) {
                log.warn("Skipping invalid pattern change event: parameter={}, dateTime={}",
                        event.getParameter(), event.getDateTime());
                continue;
            }

            PlanBoundary boundary = PlanBoundary.builder()
                    .plan(event.getParameter())
                    .planStart(event.getDateTime())
                    .build();
            eventBoundaries.add(boundary);
        }

        return eventBoundaries;
    }

    /**
     * Sets the nextPlanStart times for all plan boundaries to properly link them.
     * <p>
     * The nextPlanStart of each boundary should be the start time of the next boundary,
     * or the analysis end time for the last boundary.
     *
     * @param planBoundaries  List of plan boundaries to link
     * @param analysisEndTime End time of the analysis period
     */
    private static void setNextPlanStartTimes(List<PlanBoundary> planBoundaries, LocalDateTime analysisEndTime) {
        if (CollectionUtils.isEmpty(planBoundaries)) {
            return;
        }

        for (int i = 0; i < planBoundaries.size() - 1; i++) {
            PlanBoundary currentBoundary = planBoundaries.get(i);
            PlanBoundary nextBoundary = planBoundaries.get(i + 1);

            currentBoundary.setNextPlanStart(nextBoundary.getPlanStart());
            log.debug("Set boundary {} nextPlanStart to {}",
                    currentBoundary.getPlan(), nextBoundary.getPlanStart());
        }

        PlanBoundary lastBoundary = planBoundaries.get(planBoundaries.size() - 1);
        lastBoundary.setNextPlanStart(analysisEndTime);

        log.debug("Set last boundary {} nextPlanStart to analysis end time: {}",
                lastBoundary.getPlan(), analysisEndTime);
    }

}
