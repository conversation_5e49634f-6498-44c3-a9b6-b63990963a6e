package com.siemens.spm.analysis.vo.splitmonitor;

import com.siemens.spm.perflog.vo.PerfLogEventVO;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.PHASE_BEGIN_GREEN;
import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.PHASE_BEGIN_RED_CLEARANCE;
import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.PHASE_BEGIN_YELLOW_CLEARANCE;
import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.PHASE_END_YELLOW_CLEARANCE;
import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.PHASE_GREEN_TERMINATION;

/**
 * Factory class for processing traffic signal performance log events and calculating traffic signal cycles.
 * <p>
 * This class provides functionality to analyze performance log events from traffic signal controllers
 * and calculate cycle timing information including green, yellow, and red phase durations.
 * The factory processes events related to split monitoring and phase transitions to determine
 * complete cycle boundaries and timing characteristics.
 * </p>
 * <p>
 * The cycle calculation process involves three main steps:
 * <ol>
 *     <li>Identifying cycle boundaries based on phase begin green events</li>
 *     <li>Associating relevant events with their respective cycles</li>
 *     <li>Calculating cycle lengths and phase timing information</li>
 * </ol>
 * </p>
 * <p>
 * This class is designed to work with performance log events from traffic signal controllers
 * and supports analysis of coordinated signal timing patterns.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2/7/2025
 */
@Slf4j
public final class CyclesFactory {

    private CyclesFactory() {
    }

    /**
     * Calculates traffic signal cycle information from a list of performance log events.
     * <p>
     * This method processes performance log events to identify cycle boundaries, associate
     * events with their respective cycles, and calculate timing information for each cycle.
     * The calculation includes green, yellow, and red phase timing for each signal phase.
     * </p>
     * <p>
     * The method performs the following operations:
     * <ol>
     *     <li>Sorts the input events chronologically</li>
     *     <li>Identifies cycle boundaries based on phase begin green events</li>
     *     <li>Associates relevant events with their corresponding cycles</li>
     *     <li>Calculates cycle lengths and phase timing information</li>
     * </ol>
     * </p>
     *
     * @param events  the list of performance log events to process. Must not be null.
     *                Events will be sorted chronologically during processing.
     * @param endTime the end time for cycle calculation. This is used as the boundary
     *                for the last cycle when no subsequent cycle start is available.
     *                Must not be null.
     * @return a list of {@link CycleResult} objects containing cycle timing information,
     * sorted by green start time and then by phase number. Returns an empty list
     * if no complete cycles can be calculated.
     * @throws NullPointerException if events or endTime is null
     */
    public static List<CycleResult> calculateCycle(List<PerfLogEventVO> events, LocalDateTime endTime) {
        events = events.stream()
                .sorted()
                .toList();
        List<CycleBoundary> boundaries = identifyCycleBoundaries(events, endTime);
        List<CycleResult> results = new ArrayList<>();
        for (CycleBoundary boundary : boundaries) {
            List<CycleEvent> cycleEvents = associateEventsWithCycles(events, boundary);
            boundary.setCycleEvents(cycleEvents);
            CycleResult cycleResult = calculateCycleLengths(boundary);
            if (cycleResult != null) {
                results.add(cycleResult);
            }
        }
        return results;
    }

    /**
     * Identifies cycle boundaries based on phase begin green events.
     * <p>
     * This method analyzes performance log events to determine where traffic signal cycles
     * begin and end. Cycle boundaries are established using phase begin green events,
     * which mark the start of each cycle for a given phase. The method groups events
     * by phase and creates boundaries between consecutive green start events.
     * </p>
     * <p>
     * For each phase, the method:
     * <ol>
     *     <li>Filters events to find phase begin green events</li>
     *     <li>Groups events by phase parameter</li>
     *     <li>Sorts events chronologically within each phase</li>
     *     <li>Creates cycle boundaries between consecutive green start events</li>
     * </ol>
     * </p>
     *
     * @param events  the list of performance log events to analyze. Must not be null.
     *                Only events with {@link PerfLogEventVO.Event#PHASE_BEGIN_GREEN} are used
     *                for boundary identification.
     * @param endTime the end time to use as the boundary for the last cycle in each phase.
     *                Must not be null.
     * @return a list of {@link CycleBoundary} objects representing the identified cycle boundaries.
     * Each boundary contains the phase number, cycle start time, and next cycle start time.
     * Returns an empty list if no phase begin green events are found.
     * @throws NullPointerException if events or endTime is null
     */
    public static List<CycleBoundary> identifyCycleBoundaries(List<PerfLogEventVO> events, LocalDateTime endTime) {

        List<PerfLogEventVO> greenStartEvents = events.stream().filter(e -> PHASE_BEGIN_GREEN.equals(e.getEvent()))
                .sorted().toList();

        List<CycleBoundary> boundaries = new ArrayList<>();

        Map<Long, List<PerfLogEventVO>> eventsPerPhase = greenStartEvents.stream()
                .collect(Collectors.groupingBy(PerfLogEventVO::getParameter));

        for (Map.Entry<Long, List<PerfLogEventVO>> entry : eventsPerPhase.entrySet()) {
            Long phase = entry.getKey();
            List<PerfLogEventVO> phaseEvents = entry.getValue();

            phaseEvents.sort(Comparator.comparing(PerfLogEventVO::getDateTime));

            for (int i = 0; i < phaseEvents.size(); i++) {
                PerfLogEventVO current = phaseEvents.get(i);
                LocalDateTime nextCycleStart = (i + 1 < phaseEvents.size()) ?
                        phaseEvents.get(i + 1).getDateTime() :
                        endTime;

                boundaries.add(new CycleBoundary(phase, current.getDateTime(), nextCycleStart));
            }
        }

        return boundaries;
    }

    /**
     * Associates performance log events with their corresponding traffic signal cycles.
     * <p>
     * This method takes a list of performance log events and cycle boundaries, then
     * determines which events belong to which cycles. Only events that are relevant
     * for split monitoring analysis (as defined by {@link #SPLIT_MONITOR_EVENT_FILTERS})
     * are processed and associated with cycles.
     * </p>
     * <p>
     * The association process:
     * <ol>
     *     <li>Filters events to include only split monitor relevant events</li>
     *     <li>For each relevant event, finds the corresponding cycle boundary</li>
     *     <li>Creates a {@link CycleEvent} linking the event to its cycle</li>
     *     <li>Sorts the resulting cycle events chronologically</li>
     * </ol>
     * </p>
     *
     * @param events   the list of performance log events to process. Must not be null.
     *                 Events are filtered to include only those relevant for split monitoring.
     * @param boundary the cycle boundary that define cycle start and end times.
     *                 Must not be null. Boundary defines the time range for a cycle.
     * @return a list of {@link CycleEvent} objects representing events associated with their
     * respective cycles, sorted chronologically by event datetime. Returns an empty
     * list if no events can be associated with cycles.
     * @throws NullPointerException if events or boundaries is null
     */
    public static List<CycleEvent> associateEventsWithCycles(List<PerfLogEventVO> events, CycleBoundary boundary) {
        List<PerfLogEventVO> relevantEvents = events.stream()
                .filter(e -> e.getParameter() == boundary.getPhase())
                .toList();

        List<CycleEvent> cycleEvents = new LinkedList<>();

        for (PerfLogEventVO event : relevantEvents) {
            if (event.getDateTime().isAfter(boundary.getNextCycleStart())) {
                log.info("Ignoring event after next cycle start: {}", event);
                break;
            }
            if (boundary.isContains(event.getParameter(), event.getDateTime())) {
                CycleEvent cycleEvent = CycleEvent.builder()
                        .phase(event.getParameter())
                        .eventNum(event.getEvent().getEventNum())
                        .datetime(event.getDateTime())
                        .ordinalNumber(event.getOrdinal())
                        .build();
                cycleEvents.add(cycleEvent);
            }
        }

        return cycleEvents;
    }

    /**
     * Calculates cycle lengths and phase timing information from cycle boundary.
     * <p>
     * This method processes cycle events within a cycle boundary to calculate detailed
     * timing information for the traffic signal cycle. It determines the start and end
     * times for green, yellow, and red phases, and creates a cycle result with timing data.
     * </p>
     * <p>
     * The calculation process:
     * <ol>
     *     <li>Extracts cycle events from the cycle boundary</li>
     *     <li>Identifies phase transition events for the boundary's phase</li>
     *     <li>Calculates green, yellow, and red phase timing</li>
     *     <li>Creates a {@link CycleResult} object with complete timing information</li>
     * </ol>
     * </p>
     *
     * @param cycleBoundary the cycle boundary containing cycle events and timing information.
     *                      Must not be null and must contain cycle events.
     * @return a {@link CycleResult} object containing cycle timing information for the
     * boundary's phase, or null if no valid cycle can be calculated.
     * @throws NullPointerException if cycleBoundary is null
     */
    public static CycleResult calculateCycleLengths(CycleBoundary cycleBoundary) {
        if (cycleBoundary == null ||
                cycleBoundary.getCycleEvents() == null ||
                cycleBoundary.getCycleEvents().isEmpty()) {
            return null;
        }

        List<CycleEvent> cycleEvents = cycleBoundary.getCycleEvents().stream()
                .filter(e -> e.phase() == cycleBoundary.getPhase())
                .sorted()
                .toList();

        if (cycleEvents.isEmpty()) {
            return null;
        }

        long phase = cycleBoundary.getPhase();
        LocalDateTime greenStart = findMaxDatetimeForEvent(cycleEvents, PHASE_BEGIN_GREEN);
        LocalDateTime greenEnd = findMaxDatetimeForEvent(cycleEvents, PHASE_GREEN_TERMINATION);
        LocalDateTime yellowStart = findMaxDatetimeForEvent(cycleEvents, PHASE_BEGIN_YELLOW_CLEARANCE);
        LocalDateTime yellowEnd = findMaxDatetimeForEvent(cycleEvents, PHASE_END_YELLOW_CLEARANCE);
        LocalDateTime redStart = findMaxDatetimeForEvent(cycleEvents, PHASE_BEGIN_RED_CLEARANCE);
        LocalDateTime redEnd = cycleBoundary.getNextCycleStart();

        return new CycleResult(phase, greenStart, greenEnd, yellowStart, yellowEnd, redStart, redEnd);
    }

    /**
     * Finds the maximum (latest) datetime for a specific event type within a list of cycle events.
     * <p>
     * This utility method searches through a list of cycle events to find the latest occurrence
     * of a specific event type. This is useful for determining the actual timing of phase
     * transitions when multiple events of the same type might occur within a cycle.
     * </p>
     * <p>
     * The method filters events by event number and returns the maximum datetime among
     * matching events. If no events of the specified type are found, null is returned.
     * </p>
     *
     * @param events    the list of cycle events to search. Must not be null.
     * @param eventType the type of event to search for. Must not be null.
     *                  The event type's event number is used for matching.
     * @return the latest {@link LocalDateTime} for the specified event type,
     * or null if no events of the specified type are found
     * @throws NullPointerException if events or eventType is null
     */
    private static LocalDateTime findMaxDatetimeForEvent(List<CycleEvent> events, PerfLogEventVO.Event eventType) {
        return events.stream()
                .filter(e -> e.eventNum() == eventType.getEventNum())
                .map(CycleEvent::datetime)
                .max(LocalDateTime::compareTo).orElse(null);
    }

}
