package com.siemens.spm.analysis.vo.splitmonitor;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> Admin
 * @version : 1.0
 * @since : 2/7/2025
 **/
@Getter
@Setter
@NoArgsConstructor
public class CycleBoundary {

    private long phase;

    private LocalDateTime cycleStart;

    private LocalDateTime nextCycleStart;

    private List<CycleEvent> cycleEvents;

    public CycleBoundary(long phase, LocalDateTime cycleStart, LocalDateTime nextCycleStart) {
        this.phase = phase;
        this.cycleStart = cycleStart;
        this.nextCycleStart = nextCycleStart;
    }

    public boolean isContains(long phase, LocalDateTime eventTime) {
        return phase == this.phase && isInsideCycleBoundary(eventTime);
    }

    private boolean isInsideCycleBoundary(LocalDateTime eventTime) {
        return !eventTime.isBefore(cycleStart) &&
                (nextCycleStart == null || eventTime.isBefore(nextCycleStart));
    }

    @Override
    public boolean equals(Object o) {
        if (!(o instanceof CycleBoundary that)) {
            return false;
        }
        return phase == that.phase && Objects.equals(cycleStart, that.cycleStart);
    }

    @Override
    public int hashCode() {
        return Objects.hash(phase, cycleStart);
    }

}
