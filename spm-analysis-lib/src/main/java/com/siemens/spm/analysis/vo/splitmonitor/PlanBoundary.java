package com.siemens.spm.analysis.vo.splitmonitor;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR> Admin
 * @version : 1.0
 * @since : 3/7/2025
 **/

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PlanBoundary {

    private long plan;

    private LocalDateTime planStart;

    private LocalDateTime nextPlanStart;

    public boolean isContains(CycleBoundary cycleBoundary) {
        return cycleBoundary.getCycleStart().isAfter(planStart) &&
                (nextPlanStart == null || cycleBoundary.getCycleStart().isBefore(nextPlanStart));
    }

    @Override
    public boolean equals(Object o) {
        if (!(o instanceof PlanBoundary that)) {
            return false;
        }
        return plan == that.plan && Objects.equals(planStart, that.planStart);
    }

    @Override
    public int hashCode() {
        return Objects.hash(plan, planStart);
    }

}
