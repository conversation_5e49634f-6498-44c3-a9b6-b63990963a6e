package com.siemens.spm.analysis.vo.splitmonitor;

import lombok.Builder;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * Represents an event that occurs within a cycle.
 */
@Builder
public record CycleEvent(long phase, int eventNum, LocalDateTime datetime, int ordinalNumber)
        implements Comparable<CycleEvent> {

    @Override
    public boolean equals(Object o) {
        if (!(o instanceof CycleEvent that)) {
            return false;
        }
        return phase == that.phase &&
                eventNum == that.eventNum &&
                ordinalNumber == that.ordinalNumber &&
                Objects.equals(datetime, that.datetime);
    }

    @Override
    public int hashCode() {
        return Objects.hash(phase, eventNum, datetime, ordinalNumber);
    }

    @Override
    public int compareTo(CycleEvent o) {
        int compareDateTime = this.datetime.compareTo(o.datetime);
        if (compareDateTime != 0) {
            return compareDateTime;
        }
        return Integer.compare(this.ordinalNumber, o.ordinalNumber);
    }

}
