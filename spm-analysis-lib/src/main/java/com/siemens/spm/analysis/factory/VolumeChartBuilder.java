package com.siemens.spm.analysis.factory;

import com.siemens.spm.analysis.aggregator.PhaseCallAggregator;
import com.siemens.spm.analysis.aggregator.PlanStatAggregator;
import com.siemens.spm.analysis.util.EventValidatorUtils;
import com.siemens.spm.analysis.vo.VolumeChartVO;
import com.siemens.spm.analysis.vo.VolumePlanStatisticsVO;
import com.siemens.spm.perflog.vo.DetectorInfoVO;
import com.siemens.spm.perflog.vo.IntersectionConfigVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;

import java.util.List;

public class VolumeChartBuilder extends PhaseChartBuilder {

    private VolumeChartVO chartVO;

    private PhaseCallAggregator volumeAggregator;

    private PlanStatAggregator<VolumePlanStatisticsVO> planStatAggregator;

    /**
     * DO NOT remove this constructor. This constructor used to create chart builder object via reflection
     *
     * @param chartVO Chart object to be built, must not be null
     * @param binSize binSize in {@link Integer}
     */
    public VolumeChartBuilder(VolumeChartVO chartVO, Integer binSize) {
        super(chartVO.getPhase());
        init(chartVO, binSize, null);

    }

    /**
     * Finalize chart building and output to referenced chartVO
     */
    @Override
    public void build() {
        volumeAggregator.fillEndBins(chartVO.getToTime());
        chartVO.setChartDataList(volumeAggregator.getBinList());

        // Add plan statistics to chart
        List<VolumePlanStatisticsVO> volumePlanStatisticsVOList = planStatAggregator.getAggregatedPlanStat();
        chartVO.addListPlanStatistics(volumePlanStatisticsVOList);

        // Overall statistics
        chartVO.setTotalDetHits(volumeAggregator.getTotalPhaseCalls());
    }

    @Override
    public void setIntersectionConfig(IntersectionConfigVO configVO) {
        volumeAggregator.setConfig(configVO);
        planStatAggregator.setConfig(configVO);
    }

    @Override
    public void putEvent(PerfLogEventVO eventVO) {
        if (!EventValidatorUtils.isCandidateInRange(eventVO, chartVO.getFromTime(), chartVO.getToTime())) {
            return;
        }

        super.putEvent(eventVO);

        volumeAggregator.putEvent(eventVO);
        planStatAggregator.putEvent(eventVO);
    }

    /**
     * Initialize states of builder. This method must be called at start of processing and before other processing
     * methods.
     *
     * @param chartVO       {@code VolumeChartVO}
     * @param binSize       size of bin
     * @param detCallPhases Detector configuration from {@link DetectorInfoVO}
     */
    private void init(VolumeChartVO chartVO, int binSize, int[][] detCallPhases) {
        this.chartVO = chartVO;

        this.volumeAggregator = new PhaseCallAggregator(chartVO.getPhase(), chartVO.getFromTime(), binSize,
                detCallPhases);
        this.planStatAggregator = new PlanStatAggregator<>(chartVO.getPhase(), null,
                VolumePlanStatisticsVO.class, chartVO.getFromTime(), chartVO.getToTime());
        this.planStatAggregator.putDetCallPhases(detCallPhases);
    }

}
