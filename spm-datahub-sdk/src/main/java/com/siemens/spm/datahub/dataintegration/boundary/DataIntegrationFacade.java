/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : DataIntegrationFacade.java
 * Project     : SPM Platform
 */
package com.siemens.spm.datahub.dataintegration.boundary;

import com.siemens.spm.common.shared.domaintype.IntersectionStatus;
import com.siemens.spm.datahub.api.boundary.DataIntegrationService;
import com.siemens.spm.datahub.api.vo.DataHubAgencyInfoVO;
import com.siemens.spm.datahub.api.vo.DataHubIntersectionConfigDataVO;
import com.siemens.spm.datahub.api.vo.DataHubIntersectionSearchRequestVO;
import com.siemens.spm.datahub.api.vo.DataHubPerfLogEventVO;
import com.siemens.spm.datahub.api.vo.response.AgencySchemaResponseVO;
import com.siemens.spm.datahub.api.vo.response.DataHubIntersectionResponseVO;
import com.siemens.spm.datahub.api.vo.response.DataHubIntersectionStatusHistoryResponseVO;
import com.siemens.spm.datahub.api.vo.response.DataHubMissingDataResponseVO;
import com.siemens.spm.datahub.api.vo.response.DataHubPerfLogChunkResponseVO;
import com.siemens.spm.datahub.api.vo.response.DataHubPerfLogDisabledDataResponseVO;
import com.siemens.spm.datahub.dataintegration.strategy.DataIntegrationStrategy;
import com.siemens.spm.datahub.exception.DataHubDisabledHistoryException;
import com.siemens.spm.datahub.exception.DataHubException;
import com.siemens.spm.datahub.exception.DataHubIntersectionsException;
import com.siemens.spm.datahub.exception.DataHubMissingDataException;
import com.siemens.spm.datahub.exception.DataHubPerfLogException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Service
@RequiredArgsConstructor
public class DataIntegrationFacade implements DataIntegrationService {

    private final DataIntegrationStrategy dataIntegrationStrategy;

    @Override
    public DataHubIntersectionResponseVO getIntersections(Integer agencyId, Integer page, Integer size)
            throws DataHubIntersectionsException {
        return dataIntegrationStrategy.getIntersections(agencyId, page, size);
    }

    @Override
    public DataHubIntersectionResponseVO getIntersectionsByFilter(DataHubIntersectionSearchRequestVO searchRequestVO)
            throws DataHubException {
        return dataIntegrationStrategy.getIntersectionsByFilter(searchRequestVO);
    }

    @Override
    public DataHubIntersectionResponseVO getAllActiveIntersections(Integer agencyId)
            throws DataHubIntersectionsException {
        DataHubIntersectionSearchRequestVO searchRequestVO = DataHubIntersectionSearchRequestVO.builder()
                .agencyId(agencyId)
                .status(IntersectionStatus.AVAILABLE.getDataHub())
                .shouldPaginate(false)
                .build();
        return dataIntegrationStrategy.getIntersectionsByFilter(searchRequestVO);
    }

    @Override
    public DataHubIntersectionResponseVO getAllIntersections(Integer agencyId) throws DataHubIntersectionsException {
        DataHubIntersectionSearchRequestVO searchRequestVO = DataHubIntersectionSearchRequestVO.builder()
                .agencyId(agencyId)
                .shouldPaginate(false)
                .build();
        return dataIntegrationStrategy.getIntersectionsByFilter(searchRequestVO);
    }

    @Override
    public DataHubIntersectionStatusHistoryResponseVO getIntersectionStatusHistories(Integer agencyId,
                                                                                     String intersectionId,
                                                                                     LocalDateTime fromTime,
                                                                                     LocalDateTime toTime)
            throws DataHubException {
        return dataIntegrationStrategy.getIntersectionStatusHistories(agencyId, intersectionId, fromTime, toTime);
    }

    @Override
    public DataHubPerfLogDisabledDataResponseVO getIntersectionStatusHistory(Integer agencyId,
                                                                             String intersectionId,
                                                                             LocalDateTime fromTime,
                                                                             LocalDateTime toTime)
            throws DataHubDisabledHistoryException {
        return dataIntegrationStrategy.getDisabledHistoryData(agencyId, intersectionId, fromTime, toTime);
    }

    @Override
    public DataHubPerfLogChunkResponseVO getPerfLog(Integer agencyID,
                                                    String intUUID,
                                                    LocalDateTime fromTime,
                                                    LocalDateTime toTime)
            throws DataHubPerfLogException {
        return dataIntegrationStrategy.getPerfLog(agencyID, intUUID, fromTime, toTime);
    }

    @Override
    public DataHubPerfLogChunkResponseVO getPerfLog(Integer agencyId,
                                                    String intUUID,
                                                    LocalDateTime fromTime,
                                                    LocalDateTime toTime,
                                                    List<Integer> eventTypes) throws DataHubPerfLogException {
        return dataIntegrationStrategy.getPerfLog(agencyId, intUUID, fromTime, toTime, eventTypes);
    }

    @Override
    public DataHubPerfLogEventVO getLastCoordPatternChanged(Integer agencyId,
                                                            String intUUID,
                                                            LocalDateTime fromTime,
                                                            LocalDateTime toTime) throws DataHubPerfLogException {
        return dataIntegrationStrategy.getLastCoordPatternChanged(agencyId, intUUID, fromTime, toTime);
    }

    @Override
    public DataHubPerfLogChunkResponseVO getPerfLogAllowNoData(Integer agencyId,
                                                               String intUUID,
                                                               LocalDateTime fromTime,
                                                               LocalDateTime toTime) throws DataHubPerfLogException {
        return dataIntegrationStrategy.getPerfLogAllowNoData(agencyId, intUUID, fromTime, toTime);
    }

    @Override
    public DataHubMissingDataResponseVO getMissingData(Integer agencyId,
                                                       String intUUID,
                                                       LocalDateTime fromTime,
                                                       LocalDateTime toTime)
            throws DataHubMissingDataException {
        return dataIntegrationStrategy.getMissingData(agencyId, intUUID, fromTime, toTime);
    }

    @Override
    public DataHubIntersectionConfigDataVO getActiveIntersectionConfig(int agencyId, String intUUID)
            throws DataHubIntersectionsException {
        return dataIntegrationStrategy.getActiveIntersectionConfig(agencyId, intUUID);
    }

    @Override
    public List<DataHubAgencyInfoVO> getProvisionedAgencies() throws DataHubException {
        return dataIntegrationStrategy.getProvisionedAgencies();
    }

    @Override
    public AgencySchemaResponseVO getAgencySchema(Integer agencyId) throws DataHubException {
        return dataIntegrationStrategy.getAgencySchema(agencyId);
    }

}
