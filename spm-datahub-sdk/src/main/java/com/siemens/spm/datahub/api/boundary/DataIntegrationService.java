/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : DataIntegrationService.java
 * Project     : SPM Platform
 */
package com.siemens.spm.datahub.api.boundary;

import com.siemens.spm.datahub.api.vo.DataHubAgencyInfoVO;
import com.siemens.spm.datahub.api.vo.DataHubIntersectionConfigDataVO;
import com.siemens.spm.datahub.api.vo.DataHubIntersectionSearchRequestVO;
import com.siemens.spm.datahub.api.vo.DataHubPerfLogEventVO;
import com.siemens.spm.datahub.api.vo.response.AgencySchemaResponseVO;
import com.siemens.spm.datahub.api.vo.response.DataHubIntersectionResponseVO;
import com.siemens.spm.datahub.api.vo.response.DataHubIntersectionStatusHistoryResponseVO;
import com.siemens.spm.datahub.api.vo.response.DataHubMissingDataResponseVO;
import com.siemens.spm.datahub.api.vo.response.DataHubPerfLogChunkResponseVO;
import com.siemens.spm.datahub.api.vo.response.DataHubPerfLogDisabledDataResponseVO;
import com.siemens.spm.datahub.exception.DataHubDisabledHistoryException;
import com.siemens.spm.datahub.exception.DataHubException;
import com.siemens.spm.datahub.exception.DataHubIntersectionsException;
import com.siemens.spm.datahub.exception.DataHubMissingDataException;
import com.siemens.spm.datahub.exception.DataHubPerfLogException;

import java.time.LocalDateTime;
import java.util.List;

/**
 * This service provides APIs for retrieving/editing user info
 */
public interface DataIntegrationService {

    /**
     * Retrieves a paginated list of intersections from the Data Hub.
     *
     * @param agencyId The ID of the agency for which to retrieve intersections.
     * @param page     The page number to retrieve. Page numbers start at 0.
     * @param size     The number of records per page.
     * @return A DataHubIntersectionResponseVO object containing the requested intersections and pagination information.
     * @throws DataHubException If an error occurs while retrieving the intersections.
     */
    DataHubIntersectionResponseVO getIntersections(Integer agencyId, Integer page, Integer size)
            throws DataHubException;

    DataHubIntersectionResponseVO getIntersectionsByFilter(DataHubIntersectionSearchRequestVO searchRequestVO)
            throws DataHubException;

    /**
     * Retrieves a list of active intersection from the Data Hub.
     * @param agencyId       The ID of the agency for which to retrieve intersections.
     * @return A DataHubIntersectionStatusHistoryResponseVO object containing the requested intersections and pagination information.
     */
    DataHubIntersectionResponseVO getAllActiveIntersections(Integer agencyId) throws DataHubIntersectionsException;

    /**
     * Retrieves a list of intersection from the Data Hub.
     *
     * @param agencyId       The ID of the agency for which to retrieve intersections.
     * @return A DataHubIntersectionResponseVO object containing the requested intersections and pagination information.
     */
    DataHubIntersectionResponseVO getAllIntersections(Integer agencyId) throws DataHubIntersectionsException;

    /**
     * Retrieves a list of intersection status histories from the Data Hub.
     *
     * @param agencyId       The ID of the agency for which to retrieve intersections.
     * @param intersectionId The ID of intersection to retrieve.
     * @return A DataHubIntersectionStatusHistoryResponseVO object containing the historical of intersection status
     * @throws DataHubException If an error occurs while retrieving the intersections.
     */
    DataHubIntersectionStatusHistoryResponseVO getIntersectionStatusHistories(Integer agencyId,
                                                                              String intersectionId,
                                                                              LocalDateTime fromTime,
                                                                              LocalDateTime toTime)
            throws DataHubException;

    /**
     * Retrieve a list of Intersection status history which indicates intersection disabled gap between fromTime and
     * toTime. fromTime and toTime should be in intersection timezone
     *
     * @param agencyId       The ID of the agency for which to retrieve intersections.
     * @param intersectionId The ID of the intersection
     * @param fromTime       LocalDateTime in intersection timezone
     * @param toTime         LocalDateTime in intersection timezone
     * @return DataHubPerfLogDisabledDataResponseVO
     * @throws DataHubDisabledHistoryException Exception when retrieve history
     */
    DataHubPerfLogDisabledDataResponseVO getIntersectionStatusHistory(Integer agencyId,
                                                                      String intersectionId,
                                                                      LocalDateTime fromTime,
                                                                      LocalDateTime toTime
    ) throws DataHubDisabledHistoryException;

    /**
     * Get PerfLog from Data Hub
     *
     * @param agencyId
     * @param intUUID
     * @param fromTime
     * @param toTime
     * @return
     * @throws DataHubPerfLogException
     */
    DataHubPerfLogChunkResponseVO getPerfLog(Integer agencyId,
                                             String intUUID,
                                             LocalDateTime fromTime,
                                             LocalDateTime toTime) throws DataHubPerfLogException;

    /**
     * Get PerfLog from Data Hub
     *
     * @param agencyId
     * @param intUUID
     * @param fromTime
     * @param toTime
     * @return
     * @throws DataHubPerfLogException
     */
    DataHubPerfLogChunkResponseVO getPerfLog(Integer agencyId,
                                             String intUUID,
                                             LocalDateTime fromTime,
                                             LocalDateTime toTime,
                                             List<Integer> eventTypes) throws DataHubPerfLogException;

    /**
     * Get last Coordination Pattern Change event from Data Hub
     *
     * @param agencyId
     * @param intUUID
     * @param fromTime
     * @param toTime
     * @return
     * @throws DataHubPerfLogException
     */
    DataHubPerfLogEventVO getLastCoordPatternChanged(Integer agencyId,
                                                     String intUUID,
                                                     LocalDateTime fromTime,
                                                     LocalDateTime toTime) throws DataHubPerfLogException;

    /**
     * Get PerfLog from Data Hub but does not throw exception if no perf log data found
     *
     * @param agencyId
     * @param intUUID
     * @param fromTime
     * @param toTime
     * @return
     * @throws DataHubPerfLogException
     */
    DataHubPerfLogChunkResponseVO getPerfLogAllowNoData(Integer agencyId,
                                                        String intUUID,
                                                        LocalDateTime fromTime,
                                                        LocalDateTime toTime)
            throws DataHubPerfLogException;

    /**
     * Get missing data from Data Hub
     *
     * @param agencyId
     * @param intUUID
     * @param fromTime Only date will be used
     * @param toTime   Only date will be used
     * @return
     * @throws DataHubMissingDataException
     */
    DataHubMissingDataResponseVO getMissingData(Integer agencyId,
                                                String intUUID,
                                                LocalDateTime fromTime,
                                                LocalDateTime toTime)
            throws DataHubMissingDataException;

    /**
     * Get active intersection config from Data Hub
     *
     * @param agencyId       The ID of the agency for which to retrieve intersections.
     * @param intersectionId The ID of the intersection
     * @return DataHubIntersectionConfigDataVO
     * @throws DataHubException Exception when retrieve active intersection config
     */
    DataHubIntersectionConfigDataVO getActiveIntersectionConfig(int agencyId, String intersectionId)
            throws DataHubException;

    List<DataHubAgencyInfoVO> getProvisionedAgencies() throws DataHubException;

    /**
     * Retrieves agency schema information by agency ID.
     *
     * @param agencyId The ID of the agency to retrieve schema information for.
     * @return An AgencySchemaResponseVO object containing the agency schema information.
     * @throws DataHubException If an error occurs while retrieving the agency schema information.
     */
    AgencySchemaResponseVO getAgencySchema(Integer agencyId) throws DataHubException;

}