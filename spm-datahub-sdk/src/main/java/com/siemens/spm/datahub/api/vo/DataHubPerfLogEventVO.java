/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : DataHubPerfLogEventVO.java
 * Project     : SPM Platform
 */
package com.siemens.spm.datahub.api.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(Include.NON_NULL)
public class DataHubPerfLogEventVO implements Serializable {

    private static final long serialVersionUID = -4777303131597724053L;

    @JsonProperty("datetime")
    private LocalDateTime dateTime;

    @JsonProperty("event")
    private int event;

    /**
     * Standard range of parameter is [0:255].
     * Siemens proprietary parameter is of long range.
     */
    @JsonProperty("parameter")
    private long parameter;

    @JsonProperty("ordinalNumber")
    private int ordinal;

}
