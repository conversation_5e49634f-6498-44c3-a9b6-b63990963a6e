/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : DataIntegrationStrategy.java
 * Project     : SPM Platform
 */
package com.siemens.spm.datahub.dataintegration.strategy;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.common.constant.AgencyConstants;
import com.siemens.spm.common.util.RestUtils;
import com.siemens.spm.datahub.api.vo.DataHubAgencyInfoVO;
import com.siemens.spm.datahub.api.vo.DataHubIntersectionConfigDataVO;
import com.siemens.spm.datahub.api.vo.DataHubIntersectionSearchRequestVO;
import com.siemens.spm.datahub.api.vo.DataHubPerfLogEventVO;
import com.siemens.spm.datahub.api.vo.response.AgencySchemaResponseVO;
import com.siemens.spm.datahub.api.vo.response.DataHubIntersectionResponseVO;
import com.siemens.spm.datahub.api.vo.response.DataHubIntersectionStatusHistoryResponseVO;
import com.siemens.spm.datahub.api.vo.response.DataHubMissingDataResponseVO;
import com.siemens.spm.datahub.api.vo.response.DataHubPerfLogChunkResponseVO;
import com.siemens.spm.datahub.api.vo.response.DataHubPerfLogDisabledDataResponseVO;
import com.siemens.spm.datahub.api.vo.response.DatahubIntersectionsResponseVO;
import com.siemens.spm.datahub.config.BeanProvider;
import com.siemens.spm.datahub.config.DataHubConfig;
import com.siemens.spm.datahub.constants.DataHubConstants;
import com.siemens.spm.datahub.exception.DataHubDisabledHistoryException;
import com.siemens.spm.datahub.exception.DataHubException;
import com.siemens.spm.datahub.exception.DataHubIntersectionsException;
import com.siemens.spm.datahub.exception.DataHubMissingDataException;
import com.siemens.spm.datahub.exception.DataHubPerfLogException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class DataIntegrationStrategy {

    private final DataHubConfig dataHubConfig;

    private final RestTemplate restTemplate;

    public DataIntegrationStrategy(DataHubConfig dataHubConfig,
                                   @Qualifier(BeanProvider.CUSTOM_REST_TEMPLATE) RestTemplate restTemplate) {
        this.dataHubConfig = dataHubConfig;
        this.restTemplate = restTemplate;
    }

    private static final String INTERSECTIONS_URL_V2 = "/api/v2/intersections";
    private static final String INTERSECTIONS_SEARCH_URL_V2 = "/api/v2/intersections/search";
    private static final String INTERSECTIONS_HISTORY_URL_V2 = "/api/v2/intersections/{0}/history?from_time={1}&to_time={2}";
    private static final String GET_SPM_DATA_V2 = "/api/v2/spm/data?intersection_id={0}&from_time={1}&to_time={2}";
    private static final String GET_SPM_DATA_ALLOW_NO_DATA_V2 = "/api/v2/perflog?intersection_id={0}&from_time={1}&to_time={2}";
    private static final String GET_SPM_DATA_FILTERED_DATA_V2 = "/api/v2/perflog";
    private static final String GET_SPM_DATA_LAST_COORD_PATTERN_CHANGED_DATA_V2 = "/api/v2/perflog/last-coord-pattern-change?intersection_id={0}&from_time={1}&to_time={2}";
    private static final String GET_SPM_DATA_MISSING_V2 = "/api/v2/spm/data/missing/{0}?from_time={1}&to_time={2}";
    private static final String GET_SPM_DATA_DISABLED_V2 = "/api/v2/spm/data/disabled/{0}?from_time={1}&to_time={2}";
    private static final String ACTIVE_INTERSECTIONS_CONFIGS_URL_V2 = "/api/v2/intersections/{0}/configs/active";
    private static final String GET_PROVISIONED_AGENCIES = "/internal/agencies/provisioned";
    private static final String GET_AGENCY_SCHEMA = "/internal/agencies/{0}";

    public DataHubIntersectionResponseVO getIntersections(Integer agencyId, Integer page, Integer size)
            throws DataHubIntersectionsException {
        ResponseEntity<DataHubIntersectionResponseVO> result;

        String dhEndpoint = dataHubConfig.getIntegrationEndpoint();

        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromUriString(dhEndpoint + INTERSECTIONS_URL_V2);

        HttpEntity<Object> entity = createHeadersEntity(agencyId);

        if (page != null) {
            uriBuilder.queryParam("page", page);
        }
        if (size != null) {
            uriBuilder.queryParam("size", size);
        }

        uriBuilder.queryParam("status", "ACTIVE");
        URI uri = uriBuilder.build().encode().toUri();

        try {
            result = restTemplate.exchange(uri, HttpMethod.GET, entity, DataHubIntersectionResponseVO.class);
        } catch (Exception e) {
            log.error("Error when requesting all intersections for Agency {}", agencyId, e);
            throw new DataHubIntersectionsException(e);
        }

        return result.getBody();
    }

    public DataHubIntersectionResponseVO getIntersectionsByFilter(DataHubIntersectionSearchRequestVO requestVO)
            throws DataHubIntersectionsException {
        ResponseEntity<DatahubIntersectionsResponseVO> result;

        String dhEndpoint = dataHubConfig.getIntegrationEndpoint();

        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromUriString(dhEndpoint + INTERSECTIONS_SEARCH_URL_V2);

        Integer agencyId = requestVO.getAgencyId();
        HttpEntity<Object> entity = createEntityWithEntity(agencyId, requestVO);
        URI uri = uriBuilder.build().encode().toUri();
        try {
            result = restTemplate.exchange(uri, HttpMethod.POST, entity, DatahubIntersectionsResponseVO.class);
        } catch (Exception e) {
            log.error("Error when requesting all intersections for Agency {}", agencyId, e);
            throw new DataHubIntersectionsException(e);
        }

        DataHubIntersectionResponseVO dataHubIntersectionResponseVO = new DataHubIntersectionResponseVO();
        dataHubIntersectionResponseVO.setItems(Objects.requireNonNull(result.getBody()).getData().getIntersections());
        dataHubIntersectionResponseVO.setTotalItems(result.getBody().getData().getTotalCount());
        return dataHubIntersectionResponseVO;
    }

    public DataHubIntersectionStatusHistoryResponseVO getIntersectionStatusHistories(Integer agencyId,
                                                                                     String intersectionId,
                                                                                     LocalDateTime fromTime,
                                                                                     LocalDateTime toTime)
            throws DataHubIntersectionsException {

        String dhEndpoint = dataHubConfig.getIntegrationEndpoint();

        String url = dhEndpoint + MessageFormat.format(INTERSECTIONS_HISTORY_URL_V2, intersectionId,
                fromTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
                toTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));

        HttpEntity<Object> entity = createHeadersEntity(agencyId);

        ResponseEntity<DataHubIntersectionStatusHistoryResponseVO> result;

        try {
            result = restTemplate.exchange(url, HttpMethod.GET, entity,
                    DataHubIntersectionStatusHistoryResponseVO.class);
        } catch (Exception e) {
            log.error("Error when requesting intersection status histories for intersectionId {}", intersectionId, e);
            throw new DataHubIntersectionsException(e);
        }

        return result.getBody();
    }

    public DataHubPerfLogChunkResponseVO getPerfLog(Integer agencyId,
                                                    String intUUID,
                                                    LocalDateTime fromTime,
                                                    LocalDateTime toTime) throws DataHubPerfLogException {

        return getPerfLogDataFromEndpoint(GET_SPM_DATA_V2, agencyId, intUUID, fromTime, toTime);
    }

    public DataHubPerfLogChunkResponseVO getPerfLog(Integer agencyId,
                                                    String intUUID,
                                                    LocalDateTime fromTime,
                                                    LocalDateTime toTime,
                                                    List<Integer> eventTypes) throws DataHubPerfLogException {

        if (agencyId == null || intUUID == null || fromTime == null || toTime == null) {
            throw new IllegalArgumentException();
        }

        String url = dataHubConfig.getIntegrationEndpoint() + GET_SPM_DATA_FILTERED_DATA_V2;

        GetPerflogDataRequestDto requestDto = new GetPerflogDataRequestDto(intUUID, fromTime, toTime, eventTypes);

        try {
            return RestUtils.postWithAgencyHeader(url, String.valueOf(agencyId), requestDto,
                    DataHubPerfLogChunkResponseVO.class).getBody();
        } catch (Exception e) {
            log.error("Error when requesting PerfLog agencyUUID={}, intUUID={}", agencyId, intUUID, e);
            throw new DataHubPerfLogException(e);
        }
    }

    public DataHubPerfLogEventVO getLastCoordPatternChanged(Integer agencyId,
                                                            String intUUID,
                                                            LocalDateTime fromTime,
                                                            LocalDateTime toTime) throws DataHubPerfLogException {

        if (agencyId == null || intUUID == null || fromTime == null || toTime == null) {
            throw new IllegalArgumentException();
        }

        String url = dataHubConfig.getIntegrationEndpoint() + MessageFormat.format(
                GET_SPM_DATA_LAST_COORD_PATTERN_CHANGED_DATA_V2, intUUID,
                fromTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
                toTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));

        try {
            return RestUtils.getWithAgencyHeader(url, String.valueOf(agencyId), DataHubPerfLogEventVO.class).getBody();
        } catch (Exception e) {
            log.error("Error when requesting PerfLog agencyUUID={}, intUUID={}", agencyId, intUUID, e);
            throw new DataHubPerfLogException(e);
        }
    }

    public DataHubPerfLogChunkResponseVO getPerfLogAllowNoData(Integer agencyId,
                                                               String intUUID,
                                                               LocalDateTime fromTime,
                                                               LocalDateTime toTime) throws DataHubPerfLogException {

        return getPerfLogDataFromEndpoint(GET_SPM_DATA_ALLOW_NO_DATA_V2, agencyId, intUUID, fromTime, toTime);
    }

    public DataHubMissingDataResponseVO getMissingData(Integer agencyId,
                                                       String intUUID,
                                                       LocalDateTime fromTime,
                                                       LocalDateTime toTime) throws DataHubMissingDataException {
        String dhEndpoint = dataHubConfig.getIntegrationEndpoint();

        DateTimeFormatter dateFormatter = DateTimeFormatter.ISO_LOCAL_DATE_TIME;

        HttpEntity<Object> entity = createHeadersEntity(agencyId);

        String url = dhEndpoint + MessageFormat.format(GET_SPM_DATA_MISSING_V2, intUUID, fromTime.format(dateFormatter),
                toTime.format(dateFormatter));

        ResponseEntity<DataHubMissingDataResponseVO> result;
        try {
            result = restTemplate.exchange(url, HttpMethod.GET, entity, DataHubMissingDataResponseVO.class);
        } catch (Exception e) {
            log.error("Error when requesting missing data agencyUUID={}, intUUID={}", agencyId, intUUID, e);
            throw new DataHubMissingDataException(e);
        }

        // Remove redundant gaps
        DataHubMissingDataResponseVO responseVO = result.getBody();
        if (responseVO == null) {
            return null;
        }
        List<LocalDateTime> missingList = responseVO.getMissingTimes();
        if (missingList == null) {
            return null;
        }
        // NOTE: Each missing entry is fromTime of an 1-hour interval
        missingList.removeIf(m -> fromTime.isAfter(m) || !toTime.isAfter(m));

        return result.getBody();
    }

    public DataHubPerfLogDisabledDataResponseVO getDisabledHistoryData(Integer agencyId,
                                                                       String intersectionId,
                                                                       LocalDateTime fromTime,
                                                                       LocalDateTime toTime)
            throws DataHubDisabledHistoryException {

        String dhEndpoint = dataHubConfig.getIntegrationEndpoint();

        DateTimeFormatter dateFormatter = DateTimeFormatter.ISO_LOCAL_DATE_TIME;

        HttpEntity<Object> entity = createHeadersEntity(agencyId);

        String url = dhEndpoint + MessageFormat.format(GET_SPM_DATA_DISABLED_V2, intersectionId,
                fromTime.format(dateFormatter), toTime.format(dateFormatter));

        ResponseEntity<DataHubPerfLogDisabledDataResponseVO> result;
        try {
            result = restTemplate.exchange(url, HttpMethod.GET, entity, DataHubPerfLogDisabledDataResponseVO.class);
        } catch (Exception e) {
            log.error("Error when requesting disabled data agencyUUID={}, intUUID={}", agencyId, intersectionId, e);
            throw new DataHubDisabledHistoryException(e);
        }

        return result.getBody();
    }

    public DataHubIntersectionConfigDataVO getActiveIntersectionConfig(int agencyId, String intUUID)
            throws DataHubIntersectionsException {
        return getActiveIntersectionConfig(ACTIVE_INTERSECTIONS_CONFIGS_URL_V2, agencyId, intUUID);
    }

    public List<DataHubAgencyInfoVO> getProvisionedAgencies() throws DataHubException {

        String url = dataHubConfig.getIntegrationEndpoint() + GET_PROVISIONED_AGENCIES;
        ResponseEntity<DataHubAgencyInfoVO[]> result = restTemplate.exchange(url, HttpMethod.GET, null,
                DataHubAgencyInfoVO[].class);
        if (result.getBody() == null) {
            return List.of();
        }
        return List.of(result.getBody());
    }

    private static HttpEntity<Object> createHeadersEntity(Integer agencyId) {
        HttpHeaders headers = new HttpHeaders();
        headers.add(DataHubConstants.AGENCY_ID_HEADER, agencyId.toString());

        return new HttpEntity<>(headers);
    }

    private static HttpEntity<Object> createEntityWithEntity(Integer agencyId, Object requestBody) {
        HttpHeaders headers = new HttpHeaders();
        headers.add(DataHubConstants.AGENCY_ID_HEADER, agencyId.toString());
        return new HttpEntity<>(requestBody, headers);
    }

    private DataHubPerfLogChunkResponseVO getPerfLogDataFromEndpoint(String endpoint,
                                                                     Integer agencyId,
                                                                     String intUUID,
                                                                     LocalDateTime fromTime,
                                                                     LocalDateTime toTime)
            throws DataHubPerfLogException {

        if (agencyId == null || intUUID == null || fromTime == null || toTime == null) {
            throw new IllegalArgumentException();
        }

        String dhEndpoint = dataHubConfig.getIntegrationEndpoint();

        HttpEntity<Object> entity = createHeadersEntity(agencyId);

        String url = dhEndpoint + MessageFormat.format(endpoint, intUUID,
                fromTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
                toTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));

        ResponseEntity<DataHubPerfLogChunkResponseVO> result;
        try {
            result = restTemplate.exchange(url, HttpMethod.GET, entity, DataHubPerfLogChunkResponseVO.class);
        } catch (Exception e) {
            log.error("Error when requesting PerfLog agencyUUID={}, intUUID={}", agencyId, intUUID, e);
            throw new DataHubPerfLogException(e);
        }

        return result.getBody();
    }

    private DataHubIntersectionConfigDataVO getActiveIntersectionConfig(String endpoint, int agencyId, String intUUID)
            throws DataHubIntersectionsException {

        validateInput(agencyId, intUUID);

        String url = buildUrl(endpoint, intUUID);
        HttpEntity<Object> entity = createHeadersEntity(agencyId);

        return executeRequest(url, entity, agencyId, intUUID);
    }

    private void validateInput(int agencyId, String intUUID) {
        if (agencyId <= 0 || intUUID == null) {
            throw new IllegalArgumentException("Invalid agency ID or intersection UUID");
        }
    }

    private String buildUrl(String endpoint, String intUUID) {
        String dhEndpoint = dataHubConfig.getIntegrationEndpoint();
        return dhEndpoint + MessageFormat.format(endpoint, intUUID);
    }

    private DataHubIntersectionConfigDataVO executeRequest(String url,
                                                           HttpEntity<Object> entity,
                                                           Integer agencyId,
                                                           String intUUID) throws DataHubIntersectionsException {
        try {
            ResponseEntity<DataHubIntersectionConfigDataVO> result = restTemplate.exchange(url, HttpMethod.GET, entity,
                    DataHubIntersectionConfigDataVO.class);
            return result.getBody();
        } catch (Exception e) {
            log.error("Error when retrieving intersection config agencyUUID={}, intUUID={}", agencyId, intUUID, e);
            throw new DataHubIntersectionsException(e);
        }
    }

    public AgencySchemaResponseVO getAgencySchema(Integer agencyId) throws DataHubException {
        if (agencyId == null) {
            throw new IllegalArgumentException("Agency ID cannot be null");
        }

        String dhEndpoint = dataHubConfig.getIntegrationEndpoint();
        try {
            String url = dhEndpoint + MessageFormat.format(GET_AGENCY_SCHEMA, agencyId.toString());

            HttpHeaders headers = new HttpHeaders();
            headers.add(AgencyConstants.AGENCY_ID_HEADER_V2, agencyId.toString());
            HttpEntity<Object> entity = new HttpEntity<>(headers);

            ResponseEntity<AgencySchemaResponseVO> response = restTemplate.exchange(url, HttpMethod.GET, entity,
                    AgencySchemaResponseVO.class
            );

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                return response.getBody();
            }

            log.error("Failed to get agency schema for agency {}. Status code: {}", agencyId, response.getStatusCode());
            throw new DataHubException("Failed to get agency schema for agency: " + agencyId);
        } catch (Exception e) {
            log.error("Error getting agency schema for agency {}: {}", agencyId, e.getMessage());
            throw new DataHubException("Error getting agency schema for agency: " + agencyId);
        }
    }

    public record GetPerflogDataRequestDto(@JsonProperty("intersection_id") String intersectionId,
                                           @JsonProperty("from_time") LocalDateTime fromTime,
                                           @JsonProperty("to_time") LocalDateTime toTime,
                                           @JsonProperty("filtered_events") List<Integer> filteredEvents) {

    }

}
