package com.siemens.spm.perflogcrawler.api.vo.request;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class IntersectionTopologyRequestSearchVO implements Serializable {

    private static final long serialVersionUID = 5383827552438417845L;

    private Integer agencyId;

    private String[] orderByColumns;

    private Integer page;

    private Integer size;
}
