// /*
//  * Copyright (C) Siemens.  All Rights Reserved.
//  *
//  * Source      : PerfLogExportController.java
//  * Project     : SPM Platform
//  */
// package com.siemens.spm.perflogcrawler.api.controller;

// import jakarta.validation.Valid;

// import org.springframework.http.HttpStatus;
// import org.springframework.http.ResponseEntity;
// import org.springframework.web.bind.annotation.GetMapping;
// import org.springframework.web.bind.annotation.PathVariable;
// import org.springframework.web.bind.annotation.PostMapping;
// import org.springframework.web.bind.annotation.RequestBody;
// import org.springframework.web.bind.annotation.RequestMapping;
// import org.springframework.web.bind.annotation.RequestParam;
// import org.springframework.web.bind.annotation.ResponseStatus;

// import com.siemens.spm.common.api.PublicController;
// import com.siemens.spm.perflogcrawler.api.vo.request.PerfLogExportRequestVO;
// import com.siemens.spm.perflogcrawler.api.vo.response.PerfLogExportEntryResultObject;
// import com.siemens.spm.perflogcrawler.api.vo.response.PerfLogExportResultObject;
// import com.siemens.spm.perflogcrawler.api.vo.response.PerfLogGetResultObject;
// import io.swagger.v3.oas.annotations.Operation;
// import io.swagger.v3.oas.annotations.Parameter;
// import io.swagger.v3.oas.annotations.enums.ParameterIn;
// import io.swagger.v3.oas.annotations.responses.ApiResponse;
// import io.swagger.v3.oas.annotations.responses.ApiResponses;
// import io.swagger.v3.oas.annotations.tags.Tag;

// @RequestMapping(PerfLogExportController.API_ROOT)
// @Tag(name = "perflog-export", description = "perfLog Export API")
// public interface PerfLogExportController extends PublicController {

//     String API_ROOT = PUBLIC_API + "/v1";
//     String PERFLOG_EXPORT = "/perflog/export";

//     String PERFLOG_EXPORT_ENTRY = "/perflog/{agencyId}/export-entry";

//     String PERFLOG_GET_EVENT_LIST = "/perflog/event-types";

//     /**
//      * Create PerfLog export task. Insert request to Redis queue
//      *
//      * @param requestVO PerfLogExportRequestVO
//      * @return ResponseEntity<SimpleResultObject>
//      */
//     @Operation(summary = "Create perflog export task")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "400", description = "Bad Request")
//     })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true,
//             in = ParameterIn.HEADER, description = "Bearer Token type")
//     @ResponseStatus(HttpStatus.OK)
//     @PostMapping(PERFLOG_EXPORT)
//     ResponseEntity<PerfLogExportResultObject> createPerfLogExportTask(
//             @Parameter(name = "Request body", description = "PerfLogExportRequestVO")
//             @Valid @RequestBody PerfLogExportRequestVO requestVO
//     );

//     /**
//      * Get signed URL export data
//      *
//      * @param exportName
//      * @param password
//      * @return
//      */
//     @Operation(summary = "Get perflog export entry")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "400", description = "Bad Request")
//     })
//     @ResponseStatus(HttpStatus.OK)
//     @GetMapping(PERFLOG_EXPORT_ENTRY)
//     ResponseEntity<PerfLogExportEntryResultObject> getPerfLogExportEntry(
//             @Parameter(name = "agencyId", description = "Agency ID")
//             @PathVariable(value = "agencyId") Integer agencyId,

//             @Parameter(name = "export_name", description = "Export name")
//             @RequestParam(value = "export_name") String exportName,

//             @Parameter(name = "password", description = "Password")
//             @RequestParam(value = "password") String password);

//     /**
//      * Get all perflog event types /api/v1/perflog/events
//      *
//      * @return ResponseEntity<PerfLogGetResultObject>
//      */
//     @Operation(summary = "Get all perflog events")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "400", description = "Bad Request")
//     })
//     @ResponseStatus(HttpStatus.OK)
//     @GetMapping(PERFLOG_GET_EVENT_LIST)
//     ResponseEntity<PerfLogGetResultObject> getAllPerfLogEventTypes();

// }
