// package com.siemens.spm.analysis.api.controller;

// import java.time.DayOfWeek;
// import java.util.List;

// import jakarta.validation.Valid;
// import jakarta.validation.constraints.NotEmpty;
// import jakarta.validation.constraints.NotNull;

// import org.springframework.http.HttpStatus;
// import org.springframework.http.ResponseEntity;
// import org.springframework.validation.annotation.Validated;
// import org.springframework.web.bind.annotation.DeleteMapping;
// import org.springframework.web.bind.annotation.GetMapping;
// import org.springframework.web.bind.annotation.PatchMapping;
// import org.springframework.web.bind.annotation.PathVariable;
// import org.springframework.web.bind.annotation.PostMapping;
// import org.springframework.web.bind.annotation.PutMapping;
// import org.springframework.web.bind.annotation.RequestBody;
// import org.springframework.web.bind.annotation.RequestMapping;
// import org.springframework.web.bind.annotation.RequestParam;
// import org.springframework.web.bind.annotation.ResponseStatus;

// import com.siemens.spm.analysis.api.vo.TemplateScheduleVO;
// import com.siemens.spm.analysis.api.vo.constraint.NotFoundSummaryTemplate;
// import com.siemens.spm.analysis.api.vo.constraint.ValidSummaryTemplate;
// import com.siemens.spm.analysis.api.vo.constraint.ValidSummaryTemplateSchedule;
// import com.siemens.spm.analysis.api.vo.enums.TemplateAggregation;
// import com.siemens.spm.analysis.api.vo.enums.TemplateStatus;
// import com.siemens.spm.analysis.api.vo.request.IntersectionIdsRequestVO;
// import com.siemens.spm.analysis.api.vo.request.summaryreport.SummaryTemplateActivateRequestVO;
// import com.siemens.spm.analysis.api.vo.request.summaryreport.SummaryTemplateCreateRequestVO;
// import com.siemens.spm.analysis.api.vo.request.summaryreport.SummaryTemplateUpdateCoreDataRequestVO;
// import com.siemens.spm.analysis.api.vo.response.IntersectionSearchResultObject;
// import com.siemens.spm.analysis.api.vo.response.TemplateScheduleResultObject;
// import com.siemens.spm.analysis.api.vo.response.summaryreport.SummaryMetricResultObject;
// import com.siemens.spm.analysis.api.vo.response.summaryreport.SummaryTemplateCoreDataResultObject;
// import com.siemens.spm.analysis.api.vo.response.summaryreport.SummaryTemplateDetailResultObject;
// import com.siemens.spm.analysis.api.vo.response.summaryreport.SummaryTemplateManipulateResultObject;
// import com.siemens.spm.analysis.api.vo.response.summaryreport.SummaryTemplateProcessResultObject;
// import com.siemens.spm.analysis.api.vo.response.summaryreport.SummaryTemplateSearchResultObject;
// import com.siemens.spm.common.api.PublicController;
// import io.swagger.v3.oas.annotations.Operation;
// import io.swagger.v3.oas.annotations.Parameter;
// import io.swagger.v3.oas.annotations.enums.ParameterIn;
// import io.swagger.v3.oas.annotations.responses.ApiResponse;
// import io.swagger.v3.oas.annotations.responses.ApiResponses;
// import io.swagger.v3.oas.annotations.tags.Tag;

// @Validated
// @Tag(name = "summary-report", description = "Summary Report Template Resources")
// @RequestMapping(SummaryTemplateController.API_ROOT)
// public interface SummaryTemplateController extends PublicController {

//     String VERSION = "/v1";
//     String SUMMARY_REPORT_RESOURCES = "/summary-report";
//     String SUMMARY_REPORT_TEMPLATE_RESOURCES = SUMMARY_REPORT_RESOURCES + "/templates";
//     String API_ROOT = PUBLIC_API + VERSION + AGENCY_RESOURCE + AGENCY_ID_RESOURCE + SUMMARY_REPORT_TEMPLATE_RESOURCES;
//     String TEMPLATE_RESOURCE = "/{template_id}";
//     String INTERSECTIONS_RESOURCE = "/intersections";
//     String SCHEDULE_RESOURCE = "/schedule";
//     String ACTIVE_TEMPLATE_RESOURCE = "/activate";
//     String TEMPLATE_INTERSECTIONS_RESOURCE = TEMPLATE_RESOURCE + INTERSECTIONS_RESOURCE;
//     String TEMPLATES_INTERSECTIONS_RESOURCE = INTERSECTIONS_RESOURCE;
//     String TEMPLATE_SCHEDULE_RESOURCE = TEMPLATE_RESOURCE + SCHEDULE_RESOURCE;
//     String TEMPLATE_METRICS_RESOURCE = "/metrics";
//     String RUN_MANUALLY_TEMPLATE_RESOURCE = TEMPLATE_RESOURCE + "/run";

//     /**
//      * Create new summary report template: POST api/v1/summary-report/templates
//      *
//      * @phase requestVO {@link SummaryTemplateCreateRequestVO}
//      * @return {@link SummaryTemplateDetailResultObject}
//      */
//     @Operation(summary = "Create a summary report template")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "400", description = "Bad Request")
//     })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
//             description = "Bearer Token type")
//     @ResponseStatus(HttpStatus.CREATED)
//     @PostMapping
//     ResponseEntity<SummaryTemplateDetailResultObject> create(
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Parameter(description = "Core data of summary report template")
//             @Valid @NotNull(message = "request_body_not_null")
//             @ValidSummaryTemplate(message = "templates.invalid_data")
//             @RequestBody
//             SummaryTemplateCreateRequestVO requestVO);

//     @Operation(description = "Retrieve a list of intersections available to add into report template depending on template_id")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "400", description = "Bad Request"),
//             @ApiResponse(responseCode = "404", description = "Not Found"),
//             @ApiResponse(responseCode = "500", description = "Internal server error")
//     })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
//             description = "Bearer Token type")
//     @ResponseStatus(HttpStatus.OK)
//     @GetMapping(TEMPLATES_INTERSECTIONS_RESOURCE + "/available")
//     ResponseEntity<IntersectionSearchResultObject> searchAvailableTemplateIntersections(
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Parameter(description = "Id of the report template to search intersections")
//             @RequestParam(value = "template_id", required = false)
//             Long templateId,

//             @Parameter(description = "Id of the intersection need to filter out")
//             @RequestParam(value = "exclude_int_ids", required = false)
//             String[] excludeIntIds,

//             @Parameter(description = "It will be used to search intersections by name, email")
//             @RequestParam(value = "text", required = false)
//             String text,

//             @Parameter(description = "Sort is used to order")
//             @RequestParam(value = "sort", required = false)
//             String[] sort,

//             @Parameter(description = "Enable pagination or not")
//             @RequestParam(value = "pagination", required = false)
//             Boolean pagination,

//             @Parameter(description = "Page is used to page the returning result")
//             @RequestParam(value = "page", required = false)
//             Integer page,

//             @Parameter(description = "Size is used to size the returning result")
//             @RequestParam(value = "size", required = false)
//             Integer size
//     );

//     /**
//      * Get core data of a summary report template by id.
//      *
//      * @phase templateId id of summary report template need to retrieve core data
//      * @return {@link SummaryTemplateCoreDataResultObject}
//      */
//     @Operation(summary = "Retrieve a summary report template core data by id")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "404", description = "Not Found")
//     })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
//             description = "Bearer Token type")
//     @ResponseStatus(HttpStatus.OK)
//     @GetMapping(TEMPLATE_RESOURCE)
//     ResponseEntity<SummaryTemplateCoreDataResultObject> getCoreData(
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Parameter(description = "Id of the summary report template will be get")
//             @Valid
//             @NotNull(message = "templates.template_id_not_null")
//             @NotFoundSummaryTemplate(message = "templates.not_found")
//             @PathVariable("template_id")
//             Long templateId);

//     /**
//      * Get schedule data of a summary report template by id
//      *
//      * @phase templateId id of template
//      * @return {@link TemplateScheduleResultObject}
//      */
//     @Operation(summary = "Retrieve a summary report template schedule data by id")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "404", description = "Not Found")
//     })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
//             description = "Bearer Token type")
//     @ResponseStatus(HttpStatus.OK)
//     @GetMapping(TEMPLATE_SCHEDULE_RESOURCE)
//     ResponseEntity<TemplateScheduleResultObject> getTemplateScheduleData(
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Parameter(description = "Id of the report template will be get schedule data")
//             @Valid @NotNull(message = "templates.template_id_not_null")
//             @NotFoundSummaryTemplate(message = "templates.not_found")
//             @PathVariable("template_id")
//             Long templateId);

//     /**
//      * Update core data of a summary report template by id
//      *
//      * @phase templateId      id of template need to update core data
//      * @phase updateRequestVO {@link SummaryTemplateUpdateCoreDataRequestVO}
//      * @return {@link SummaryTemplateManipulateResultObject}
//      */
//     @Operation(summary = "Update core data of a report template by id")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "404", description = "Not Found")
//     })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
//             description = "Bearer Token type")
//     @ResponseStatus(HttpStatus.OK)
//     @PutMapping(TEMPLATE_RESOURCE)
//     ResponseEntity<SummaryTemplateManipulateResultObject> updateCoreData(
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Parameter(description = "Id of the summary report template will be update core data")
//             @Valid @NotNull(message = "templates.template_id_not_null")
//             @NotFoundSummaryTemplate(message = "templates.not_found")
//             @PathVariable("template_id")
//             Long templateId,

//             @Parameter(description = "information about template to update")
//             @Valid @NotNull(message = "request_body_not_null")
//             @ValidSummaryTemplate(message = "templates.invalid_data")
//             @RequestBody SummaryTemplateUpdateCoreDataRequestVO updateRequestVO
//     );

//     /**
//      * Update schedule data of a summary report template by id
//      *
//      * @phase templateId         id of summary template need to update core data
//      * @phase templateScheduleVO {@link TemplateScheduleVO}
//      * @return {@link SummaryTemplateManipulateResultObject}
//      */
//     @Operation(summary = "Update schedule data of a summary report template by id")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "404", description = "Not Found")
//     })

//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
//             description = "Bearer Token type")
//     @ResponseStatus(HttpStatus.OK)
//     @PutMapping(TEMPLATE_SCHEDULE_RESOURCE)
//     ResponseEntity<SummaryTemplateManipulateResultObject> updateTemplateScheduleData(
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Parameter(description = "Id of the report template will be update schedule data")
//             @Valid @NotNull(message = "templates.template_id_not_null")
//             @NotFoundSummaryTemplate(message = "templates.not_found")
//             @PathVariable("template_id")
//             Long templateId,

//             @Parameter(description = "information about schedule data to update")
//             @Valid @NotNull(message = "request_body_not_null")
//             @ValidSummaryTemplateSchedule(message = "templates.schedule.invalid")
//             @RequestBody
//             TemplateScheduleVO templateScheduleVO
//     );

//     /**
//      * Delete multiple report templates by list of template ids This method is SOFT delete
//      *
//      * @phase templateIds {@link List<Long>} List of template ids need to delete
//      * @return {@link SummaryTemplateManipulateResultObject} object
//      */
//     @Operation(summary = "Delete multiple report templates")
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
//             description = "Bearer Token type")
//     @ResponseStatus(HttpStatus.NO_CONTENT)
//     @DeleteMapping
//     ResponseEntity<SummaryTemplateManipulateResultObject> deletes(
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Parameter(description = "List of id of report templates will be delete")

//             @Valid @NotEmpty(message = "templates.template_ids_not_empty")
//             @RequestParam(value = "template_ids")
//             List<Long> templateIds
//     );

//     /**
//      * Update intersection(s) to a specific report template(delete or add depends on action):
//      * <p>
//      * PATCH: /summary-report/templates/{template_id}/intersections
//      *
//      * @phase templateId               id of template need to update intersection(s)
//      * @phase intersectionIdsRequestVO {@link IntersectionIdsRequestVO} contain information about intersection(s) need
//      *                                 to update
//      * @return {@link SummaryTemplateManipulateResultObject}
//      */
//     @Operation(summary = "Update intersections to a specific report template(delete or add depends on action)")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "400", description = "Bad Request")
//     })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
//             description = "Bearer Token type")
//     @ResponseStatus(HttpStatus.NO_CONTENT)
//     @PatchMapping(TEMPLATE_INTERSECTIONS_RESOURCE)
//     ResponseEntity<SummaryTemplateManipulateResultObject> updateIntersections(
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Parameter(description = "Id of a report template that intersections will be update")
//             @Valid @NotNull(message = "templates.template_id_not_null")
//             @NotFoundSummaryTemplate(message = "templates.not_found")
//             @PathVariable("template_id")
//             Long templateId,

//             @Parameter(description = "List of ids of intersections to be update")
//             @Valid @RequestBody
//             IntersectionIdsRequestVO intersectionIdsRequestVO
//     );

//     /**
//      * Update status of multiple summary report templates(ACTIVE or INACTIVE):
//      * <p>
//      * PATCH /summary-report/templates/activate
//      *
//      * @return {@link ResponseEntity<SummaryTemplateManipulateResultObject>}
//      */
//     @Operation(summary = "Activate multiple summary report templates")
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
//             description = "Bearer Token type")
//     @ResponseStatus(HttpStatus.NO_CONTENT)
//     @PatchMapping(ACTIVE_TEMPLATE_RESOURCE)
//     ResponseEntity<SummaryTemplateManipulateResultObject> activate(
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Parameter(description = "List of ids of summary report templates to be update status.")
//             @Valid
//             @RequestBody
//             SummaryTemplateActivateRequestVO activeRequestVO
//     );

//     /**
//      * Retrieve a list of intersections depending on request phase
//      *
//      * @phase templateId     id of template
//      * @phase text           text to search (name, email, ...)
//      * @phase status         status (ACTIVATE or INACTIVE)
//      * @phase orderByColumns order by columns
//      * @phase page           page
//      * @phase size           size in page
//      * @return {@link IntersectionSearchResultObject}
//      */
//     @Operation(summary = "Retrieve a list of intersections depending on template_id")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "400", description = "Bad Request"),
//             @ApiResponse(responseCode = "404", description = "Not Found"),
//             @ApiResponse(responseCode = "500", description = "Internal server error")
//     })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
//             description = "Bearer Token type")
//     @ResponseStatus(HttpStatus.OK)
//     @GetMapping(TEMPLATE_INTERSECTIONS_RESOURCE)
//     ResponseEntity<IntersectionSearchResultObject> searchTemplateIntersections(
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Parameter(description = "Id of the report template to search intersections")
//             @Valid
//             @NotNull(message = "templates.template_id_not_null")
//             @NotFoundSummaryTemplate(message = "templates.not_found")
//             @PathVariable("template_id")
//             Long templateId,

//             @Parameter(description = "It will be used to search intersections by name, email")
//             @RequestParam(value = "text", required = false)
//             String text,

//             @Parameter(description = "It will be used to search intersections by status")
//             @RequestParam(value = "status", required = false)
//             String status,

//             @Parameter(description = "Fields will be used to sort returning data")
//             @RequestParam(value = "sort", required = false)
//             String[] orderByColumns,

//             @Parameter(description = "Page is used to page the returning result")
//             @RequestParam(value = "page", required = false)
//             Integer page,

//             @Parameter(description = "Size is used to size the returning result")
//             @RequestParam(value = "size", required = false)
//             Integer size
//     );

//     /**
//      * Search all summary report templates by filters, pagination and sorting
//      *
//      * @phase agencyId       id of agency
//      * @phase text           text to search (name, description, ..)
//      * @phase fromDate       create time greater than fromDate
//      * @phase toDate         create time smaller than toDate
//      * @phase weekDays       weeks day of template(s)
//      * @phase aggregation    aggregation unit of template(s)
//      * @phase ownerId        id of owner of template(s)
//      * @phase status         status of template(s)
//      * @phase orderByColumns order by columns
//      * @phase page           page
//      * @phase size           size of page
//      * @return {@link SummaryTemplateSearchResultObject}
//      */
//     @Operation(summary = "Get all summary report templates with filters, pagination and sorting")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "400", description = "Bad Request"),
//             @ApiResponse(responseCode = "404", description = "Not Found"),
//             @ApiResponse(responseCode = "500", description = "Internal server error")
//     })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
//             description = "Bearer Token type")
//     @ResponseStatus(HttpStatus.OK)
//     @GetMapping
//     ResponseEntity<SummaryTemplateSearchResultObject> search(
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Parameter(description = "Text to search")
//             @RequestParam(value = "text", required = false)
//             String text,

//             @Parameter(description = "This day must be lower than created_at")
//             @RequestParam(value = "from_date", required = false)
//             Long fromDate,

//             @Parameter(description = "This day must be greater than created_at")
//             @RequestParam(value = "to_date", required = false)
//             Long toDate,

//             @Parameter(description = "Day of the week must be between MONDAY -> SUNDAY")
//             @RequestParam(value = "week_days", required = false)
//             DayOfWeek[] weekDays,

//             @Parameter(description = "Aggregation unit of template")
//             @RequestParam(value = "aggregation", required = false)
//             TemplateAggregation aggregation,

//             @Parameter(description = "Id of an owner that the template belong to")
//             @RequestParam(value = "owner_id", required = false)
//             Long ownerId,

//             @Parameter(description = "The status of template")
//             @RequestParam(value = "status", required = false)
//             TemplateStatus status,

//             @Parameter(description = "Fields will be used to sort returning data")
//             @RequestParam(value = "sort", required = false)
//             String[] orderByColumns,

//             @Parameter(description = "Page is used to page the returning result")
//             @RequestParam(value = "page", required = false)
//             Integer page,

//             @Parameter(description = "Size is used to size the returning result")
//             @RequestParam(value = "size", required = false)
//             Integer size
//     );

//     /**
//      * Run manually a summary report template by id
//      *
//      * @phase templateId id of report template
//      * @return {@link SummaryTemplateProcessResultObject}
//      */
//     @Operation(summary = "Run manually summary a report template by id")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "404", description = "Not Found")
//     })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
//             description = "Bearer Token type")
//     @ResponseStatus(HttpStatus.OK)
//     @PostMapping(RUN_MANUALLY_TEMPLATE_RESOURCE)
//     ResponseEntity<SummaryTemplateProcessResultObject> runSummaryTemplate(
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Parameter(description = "Id of the report template will be run")
//             @Valid @NotNull(message = "templates.template_id_not_null")
//             @NotFoundSummaryTemplate(message = "templates.not_found")
//             @PathVariable("template_id") Long templateId
//     );

//     @Operation(summary = "Get all available metrics in templates")
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
//             description = "Bearer Token type")
//     @ResponseStatus(HttpStatus.OK)
//     @GetMapping(TEMPLATE_METRICS_RESOURCE)
//     ResponseEntity<SummaryMetricResultObject> getAllMetrics(@PathVariable(AGENCY_ID) Integer agencyId);

// }
