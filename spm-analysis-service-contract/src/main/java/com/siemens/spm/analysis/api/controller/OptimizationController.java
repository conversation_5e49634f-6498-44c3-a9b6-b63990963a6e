// package com.siemens.spm.analysis.api.controller;

// import java.time.LocalTime;
// import java.util.List;

// import jakarta.validation.Valid;
// import jakarta.validation.constraints.NotEmpty;

// import org.springframework.format.annotation.DateTimeFormat;
// import org.springframework.http.HttpStatus;
// import org.springframework.http.ResponseEntity;
// import org.springframework.web.bind.annotation.DeleteMapping;
// import org.springframework.web.bind.annotation.GetMapping;
// import org.springframework.web.bind.annotation.PathVariable;
// import org.springframework.web.bind.annotation.PostMapping;
// import org.springframework.web.bind.annotation.RequestBody;
// import org.springframework.web.bind.annotation.RequestMapping;
// import org.springframework.web.bind.annotation.RequestParam;
// import org.springframework.web.bind.annotation.ResponseStatus;

// import com.siemens.spm.analysis.api.vo.optimization.OptimizationRequestVO;
// import com.siemens.spm.analysis.api.vo.response.CreatingOptimizationResultObject;
// import com.siemens.spm.analysis.api.vo.response.OptimizationHistoryResultObject;
// import com.siemens.spm.analysis.api.vo.response.OptimizationMetadataResultObject;
// import com.siemens.spm.analysis.api.vo.response.OptimizationResultObject;
// import com.siemens.spm.common.api.PublicController;
// import com.siemens.spm.common.shared.vo.SimpleResultObject;
// import io.swagger.v3.oas.annotations.Operation;
// import io.swagger.v3.oas.annotations.Parameter;
// import io.swagger.v3.oas.annotations.enums.ParameterIn;
// import io.swagger.v3.oas.annotations.responses.ApiResponse;
// import io.swagger.v3.oas.annotations.responses.ApiResponses;
// import io.swagger.v3.oas.annotations.tags.Tag;

// /**
//  * <AUTHOR> Nguyen
//  */
// @Tag(name = "optimization")
// @RequestMapping(OptimizationController.API_ROOT)
// public interface OptimizationController extends PublicController {

//     String VERSION = "/v1";
//     String OPTIMIZATION_RESOURCES = "/optimization";
//     String API_ROOT = PUBLIC_API + VERSION + AGENCY_RESOURCE + AGENCY_ID_RESOURCE + OPTIMIZATION_RESOURCES;

//     String METADATA_API = "/metadata";
//     String HISTORIES_RESOURCE = "/histories";

//     String OPTIMIZATION_ID = "optimizationId";
//     String OPTIMIZATION_RESOURCE = "/{" + OPTIMIZATION_ID + "}";

//     /**
//      * get all of supported optimization metadata
//      *
//      * @return
//      */
//     @Operation(summary = "Get the optimization metadata: supported types, plan numbers")
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
//             description = "Bearer Token type")
//     @GetMapping(METADATA_API)
//     ResponseEntity<OptimizationMetadataResultObject> getMetadata(@PathVariable(AGENCY_ID) Integer agencyId);

//     /**
//      * Search the history of optimization
//      *
//      * @phase text
//      * @phase optimizeType
//      * @phase status
//      * @phase orderByColumns
//      * @phase page
//      * @phase size
//      * @return
//      */
//     @Operation(summary = "Get the histories of optimization")
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
//             description = "Bearer Token type")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "400", description = "Bad Request or data is not available"),
//     })
//     @GetMapping(HISTORIES_RESOURCE)
//     ResponseEntity<OptimizationHistoryResultObject> searchHistories(
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Parameter(name = "text", description = "Name of intersection/corridor")
//             @RequestParam(value = "text", required = false) String text,

//             @Parameter(name = "type", description = "Optimization Type")
//             @RequestParam(value = "type", required = false) String optimizeType,

//             @Parameter(name = "status", description = "IN_PROGRESS/COMPLETED/ERROR")
//             @RequestParam(value = "status", required = false) String status,

//             @Parameter(name = "sort",
//                     description = "Sortable columns are: {created_time, intersection_name, type, status}. Default order is ascending.")
//             @RequestParam(value = "sort", required = false) String[] orderByColumns,

//             @Parameter(name = "page", description = "Page number. If page is empty, it will be 0 (default).")
//             @RequestParam(value = "page", required = false) Integer page,

//             @Parameter(name = "size", description = "Size of page. If size is empty, it will be 20 (default)")
//             @RequestParam(value = "size", required = false) Integer size
//     );

//     /**
//      * Delete optimization histories by list of specific ids
//      *
//      * @phase ids List of optimization history's id
//      * @return {@link SimpleResultObject}
//      */
//     @Operation(summary = "Delete histories of optimization")
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
//             description = "Bearer Token type")
//     @DeleteMapping(HISTORIES_RESOURCE)
//     ResponseEntity<SimpleResultObject> deleteHistories(
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Parameter(description = "List of id of optimization histories will be delete")
//             @Valid @NotEmpty(message = "optimization.validation.id_list_not_empty")
//             @RequestParam("ids") List<Long> ids);

//     @Operation(summary = "View result of an optimization")
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
//             description = "Bearer Token type")
//     @GetMapping(OPTIMIZATION_RESOURCE)
//     ResponseEntity<OptimizationResultObject> getOptimizationResult(
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Parameter(name = OPTIMIZATION_ID, description = "Optimization Id")
//             @PathVariable(OPTIMIZATION_ID) final Long optimizationId,

//             @Parameter(name = "type", description = "type of optimization")
//             @RequestParam("type") String type,

//             @Parameter(description = "start time to filter result")
//             @RequestParam(value = "from_time", required = false)
//             @DateTimeFormat(iso = DateTimeFormat.ISO.TIME)
//             LocalTime fromTime,

//             @Parameter(description = "end time to filter result")
//             @RequestParam(value = "to_time", required = false)
//             @DateTimeFormat(iso = DateTimeFormat.ISO.TIME)
//             LocalTime toTime
//     );

//     /**
//      * Create(create and process) an offset optimization
//      *
//      * @phase request {@link CreatingOptimizationResultObject}
//      * @return {@link CreatingOptimizationResultObject}
//      */
//     @Operation(summary = "Create(create and process) an optimization")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "400", description = "Bad Request or data is not available"),
//     })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
//             description = "Bearer Token type")
//     @ResponseStatus(HttpStatus.CREATED)
//     @PostMapping
//     ResponseEntity<CreatingOptimizationResultObject> beginOptimize(
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Parameter(name = "request_body", description = "Optimization request")
//             @Valid @RequestBody OptimizationRequestVO request);

// }
