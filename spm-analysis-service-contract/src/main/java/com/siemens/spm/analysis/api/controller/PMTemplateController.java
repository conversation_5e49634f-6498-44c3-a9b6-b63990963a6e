// package com.siemens.spm.analysis.api.controller;

// import java.util.List;

// import jakarta.validation.Valid;
// import jakarta.validation.constraints.NotEmpty;
// import jakarta.validation.constraints.NotNull;

// import org.springframework.http.HttpStatus;
// import org.springframework.http.ResponseEntity;
// import org.springframework.validation.annotation.Validated;
// import org.springframework.web.bind.annotation.DeleteMapping;
// import org.springframework.web.bind.annotation.GetMapping;
// import org.springframework.web.bind.annotation.PatchMapping;
// import org.springframework.web.bind.annotation.PathVariable;
// import org.springframework.web.bind.annotation.PostMapping;
// import org.springframework.web.bind.annotation.PutMapping;
// import org.springframework.web.bind.annotation.RequestBody;
// import org.springframework.web.bind.annotation.RequestMapping;
// import org.springframework.web.bind.annotation.RequestParam;
// import org.springframework.web.bind.annotation.ResponseStatus;

// import com.siemens.spm.analysis.api.vo.TemplateScheduleVO;
// import com.siemens.spm.analysis.api.vo.constraint.ValidPMTemplateSchedule;
// import com.siemens.spm.analysis.api.vo.enums.TemplateStatus;
// import com.siemens.spm.analysis.api.vo.performancemetric.PMTemplateSimpleDataVO;
// import com.siemens.spm.analysis.api.vo.request.IntersectionIdsRequestVO;
// import com.siemens.spm.analysis.api.vo.request.performancemetric.PMTemplateActiveRequestVO;
// import com.siemens.spm.analysis.api.vo.request.performancemetric.PMTemplateCreateRequestVO;
// import com.siemens.spm.analysis.api.vo.response.AnalysisMetadataResultObject;
// import com.siemens.spm.analysis.api.vo.response.IntersectionSearchResultObject;
// import com.siemens.spm.analysis.api.vo.response.TemplateScheduleResultObject;
// import com.siemens.spm.analysis.api.vo.response.performancemetric.PMTemplateDetailResultObject;
// import com.siemens.spm.analysis.api.vo.response.performancemetric.PMTemplateGeneralDataResultObject;
// import com.siemens.spm.analysis.api.vo.response.performancemetric.PMTemplateManipulateResultObject;
// import com.siemens.spm.analysis.api.vo.response.performancemetric.PMTemplateProcessResultObject;
// import com.siemens.spm.analysis.api.vo.response.performancemetric.PMTemplateSearchResultObject;
// import com.siemens.spm.common.api.PublicController;
// import io.swagger.v3.oas.annotations.Operation;
// import io.swagger.v3.oas.annotations.Parameter;
// import io.swagger.v3.oas.annotations.enums.ParameterIn;
// import io.swagger.v3.oas.annotations.responses.ApiResponse;
// import io.swagger.v3.oas.annotations.responses.ApiResponses;
// import io.swagger.v3.oas.annotations.tags.Tag;

// @Tag(name = "performance-metric", description = "Performance metric template resource")
// @Validated
// @RequestMapping(PMTemplateController.API_ROOT)
// public interface PMTemplateController extends PublicController {

//     String VERSION = "/v1";
//     String PM_RESOURCES = "/performance-metric";
//     String PM_TEMPLATE_RESOURCES = PM_RESOURCES + "/templates";
//     String API_ROOT = PUBLIC_API + VERSION + AGENCY_RESOURCE + AGENCY_ID_RESOURCE + PM_TEMPLATE_RESOURCES;

//     @Operation(summary = "Create a new performance metric template")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "400", description = "Bad Request")
//     })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
//             description = "Bearer Token type")
//     @ResponseStatus(HttpStatus.CREATED)
//     @PostMapping
//     ResponseEntity<PMTemplateDetailResultObject> create(
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Parameter(description = "Request data to create performance metric template")
//             @Valid @NotNull(message = "request_body_not_null")
//             @RequestBody
//             PMTemplateCreateRequestVO requestVO);

//     /**
//      * Retrieve a list of intersections available to add into a performance metric
//      *
//      * @phase agencyId   id of agency which intersection belong to
//      * @phase templateId id of template which intersection will configured
//      * @phase text       name of intersection
//      * @phase sort
//      * @phase page       page of paging
//      * @phase size       size of paging
//      * @return list of intersection
//      */
//     @Operation(summary = "Retrieve a list of intersections available to add into performance metric template")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "400", description = "Bad Request"),
//             @ApiResponse(responseCode = "404", description = "Not Found"),
//             @ApiResponse(responseCode = "500", description = "Internal server error")
//     })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
//             description = "Bearer Token type")
//     @ResponseStatus(HttpStatus.OK)
//     @GetMapping("/intersections/available")
//     ResponseEntity<IntersectionSearchResultObject> searchAvailableTemplateIntersections(
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Parameter(description = "Id of the report template to search intersections")
//             @RequestParam(value = "template_id", required = false)
//             Long templateId,

//             @Parameter(description = "Id of the intersection need to filter out")
//             @RequestParam(value = "exclude_int_ids", required = false)
//             String[] excludeIntIds,

//             @Parameter(description = "It will be used to search intersections by name, email")
//             @RequestParam(value = "text", required = false)
//             String text,

//             @Parameter(description = "Sort by column")
//             @RequestParam(value = "sort", required = false)
//             String[] sort,

//             @Parameter(description = "Enable pagination or not")
//             @RequestParam(value = "pagination", required = false)
//             Boolean pagination,

//             @Parameter(description = "Page is used to page the returning result")
//             @RequestParam(value = "page", required = false)
//             Integer page,

//             @Parameter(description = "Size is used to size the returning result")
//             @RequestParam(value = "size", required = false)
//             Integer size
//     );

//     /**
//      * Search all performance metric templates by filters, pagination and sorting
//      *
//      * @phase agencyId       id of agency
//      * @phase metricTypeId   id of metric type
//      * @phase fromDate       create time greater than fromDate
//      * @phase toDate         create time smaller than toDate
//      * @phase ownerId        id of owner of template(s)
//      * @phase status         status of template(s)
//      * @phase orderByColumns order by columns
//      * @phase page           page number
//      * @phase size           size of page
//      * @return {@link PMTemplateSearchResultObject}
//      */
//     @Operation(summary = "Get all performance metric templates with filters, pagination and sorting")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "400", description = "Bad Request"),
//             @ApiResponse(responseCode = "404", description = "Not Found"),
//             @ApiResponse(responseCode = "500", description = "Internal server error")
//     })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
//             description = "Bearer Token type")
//     @ResponseStatus(HttpStatus.OK)
//     @GetMapping
//     ResponseEntity<PMTemplateSearchResultObject> search(
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Parameter(description = "It will be used to search by description")
//             @RequestParam(value = "text", required = false)
//             String text,

//             @Parameter(description = "Metric type id of templates")
//             @RequestParam(value = "metric_type", required = false)
//             String metricTypeId,

//             @Parameter(description = "This day must be lower than created_at")
//             @RequestParam(value = "from_date", required = false)
//             Long fromDate,

//             @Parameter(description = "This day must be greater than created_at")
//             @RequestParam(value = "to_date", required = false)
//             Long toDate,

//             @Parameter(description = "Id of an owner that the template belong to")
//             @RequestParam(value = "owner_id", required = false)
//             Long ownerId,

//             @Parameter(description = "The status of template")
//             @RequestParam(value = "status", required = false)
//             TemplateStatus status,

//             @Parameter(description = "Fields will be used to sort returning data")
//             @RequestParam(value = "sort", required = false)
//             String[] orderByColumns,

//             @Parameter(description = "Page is used to page the returning result")
//             @RequestParam(value = "page", required = false)
//             Integer page,

//             @Parameter(description = "Size is used to size the returning result")
//             @RequestParam(value = "size", required = false)
//             Integer size
//     );

//     /**
//      * Get general data of a performance metric template by id.
//      *
//      * @phase templateId id of performance metric template need to retrieve general data
//      * @return {@link PMTemplateGeneralDataResultObject}
//      */
//     @Operation(summary = "Retrieve a performance metric template general data by id")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "200", description = "Ok"),
//             @ApiResponse(responseCode = "404", description = "Not Found")
//     })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
//             description = "Bearer Token type")
//     @ResponseStatus(HttpStatus.OK)
//     @GetMapping("/{template_id}")
//     ResponseEntity<PMTemplateGeneralDataResultObject> getGeneralData(
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Parameter(description = "Id of the performance metric template will be get")
//             @PathVariable("template_id")
//             Long templateId);

//     /**
//      * Get schedule data of a performance metric template by id
//      *
//      * @phase templateId id of template
//      * @return {@link TemplateScheduleResultObject}
//      */
//     @Operation(summary = "Retrieve a performance metric template schedule data by id")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "200", description = "Ok"),
//             @ApiResponse(responseCode = "404", description = "Not Found")
//     })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
//             description = "Bearer Token type")
//     @ResponseStatus(HttpStatus.OK)
//     @GetMapping("/{template_id}/schedule")
//     ResponseEntity<TemplateScheduleResultObject> getScheduleData(
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Parameter(description = "Id of the performance metric to retrieve schedule data")
//             @PathVariable("template_id")
//             Long templateId);

//     /**
//      * Update general data of a performance metric template by id
//      *
//      * @phase templateId      id of template need to update core data
//      * @phase updateRequestVO {@link PMTemplateSimpleDataVO} data to update
//      * @return {@link PMTemplateManipulateResultObject}
//      */
//     @Operation(summary = "Update general data of a performance metric template by id")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "200", description = "Ok"),
//             @ApiResponse(responseCode = "404", description = "Not Found")
//     })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
//             description = "Bearer Token type")
//     @ResponseStatus(HttpStatus.OK)
//     @PutMapping("/{template_id}")
//     ResponseEntity<PMTemplateManipulateResultObject> updateGeneralData(
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Parameter(description = "Id of the performance metric template will be update core data")
//             @PathVariable("template_id")
//             Long templateId,

//             @Parameter(description = "information about template to update")
//             @Valid @NotNull(message = "request_body_not_null")
//             @RequestBody
//             PMTemplateSimpleDataVO updateRequestVO
//     );

//     /**
//      * Update schedule data of a performance metric template by id
//      *
//      * @phase templateId         id of template need to update
//      * @phase templateScheduleVO {@link TemplateScheduleVO}
//      * @return {@link PMTemplateManipulateResultObject}
//      */
//     @Operation(summary = "Update schedule data of a summary report template by id")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "404", description = "Not Found")
//     })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
//             description = "Bearer Token type")
//     @ResponseStatus(HttpStatus.OK)
//     @PutMapping("/{template_id}/schedule")
//     ResponseEntity<PMTemplateManipulateResultObject> updateScheduleData(
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Parameter(description = "id of template will be update schedule data")
//             @Valid @NotNull
//             @PathVariable("template_id")
//             Long templateId,

//             @Parameter(description = "information about schedule data to update")
//             @Valid @NotNull(message = "request_body_not_null")
//             @ValidPMTemplateSchedule(message = "templates.schedule.invalid")
//             @RequestBody
//             TemplateScheduleVO templateScheduleVO
//     );

//     /**
//      * Update intersection(s) to a specific performance metric template(delete or add depends on action):
//      *
//      * @phase templateId               id of template need to update intersection(s)
//      * @phase intersectionIdsRequestVO {@link IntersectionIdsRequestVO} contain information about intersection(s) need
//      *                                 to update
//      * @return {@link PMTemplateManipulateResultObject}
//      */
//     @Operation(description = "Update intersections to a specific performance metric template(delete or add depends on action")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "400", description = "Bad Request")
//     })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
//             description = "Bearer Token type")
//     @ResponseStatus(HttpStatus.NO_CONTENT)
//     @PatchMapping("/{template_id}/intersections")
//     ResponseEntity<PMTemplateManipulateResultObject> updateIntersections(
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Parameter(description = "Id of a performance metric template that intersections will be update")
//             @PathVariable("template_id")
//             Long templateId,

//             @Parameter(description = "List of ids of intersections to be update")
//             @Valid @RequestBody
//             IntersectionIdsRequestVO intersectionIdsRequestVO
//     );

//     /**
//      * Retrieve a list of intersections configured in template depending on request phase
//      *
//      * @phase templateId     id of template
//      * @phase text           text to search (name, email, ...)
//      * @phase status         status (ACTIVATE or INACTIVE)
//      * @phase orderByColumns order by columns
//      * @phase page           page
//      * @phase size           size in page
//      * @return {@link IntersectionSearchResultObject}
//      */
//     @Operation(summary = "Retrieve a list of intersections depending on template_id")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "404", description = "Not Found")
//     })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
//             description = "Bearer Token type")
//     @ResponseStatus(HttpStatus.OK)
//     @GetMapping("/{template_id}/intersections")
//     ResponseEntity<IntersectionSearchResultObject> searchTemplateIntersections(
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Parameter(description = "Id of the performance metric template to search intersections")
//             @PathVariable("template_id")
//             Long templateId,

//             @Parameter(description = "It will be used to search intersections by name, email")
//             @RequestParam(value = "text", required = false)
//             String text,

//             @Parameter(description = "It will be used to search intersections by status")
//             @RequestParam(value = "status", required = false)
//             String status,

//             @Parameter(description = "Fields will be used to sort returning data")
//             @RequestParam(value = "sort", required = false)
//             String[] orderByColumns,

//             @Parameter(description = "Page is used to page the returning result")
//             @RequestParam(value = "page", required = false)
//             Integer page,

//             @Parameter(description = "Size is used to size the returning result")
//             @RequestParam(value = "size", required = false)
//             Integer size
//     );

//     /**
//      * Api that soft delete a performance metric template
//      *
//      * @phase ids contains List ids to specific templates that need delete
//      * @return {@code PMTemplateManipulateResultObject} object
//      */
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
//             description = "Bearer Token type")
//     @ResponseStatus(HttpStatus.OK)
//     @DeleteMapping
//     ResponseEntity<PMTemplateManipulateResultObject> deleteTemplates(
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Parameter(description = "Request data to delete performance metric template")
//             @Valid @NotEmpty(message = "performance_metric.templates.temp_ids_not_empty")
//             @RequestParam(value = "template_ids") List<Long> ids);

//     /**
//      * Api that active or inactive a performance metric template
//      *
//      * @phase pmActiveVO contains List ids to specific templates that need update Status : template's status
//      * @return {@code PMTemplateManipulateResultObject} object
//      */
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
//             description = "Bearer Token type")
//     @ResponseStatus(HttpStatus.OK)
//     @PutMapping("/active")
//     ResponseEntity<PMTemplateManipulateResultObject> activeTemplates(
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Parameter(description = "Request data to active performance metric template")
//             @Valid @RequestBody PMTemplateActiveRequestVO pmActiveVO);

//     /**
//      * Run manually a performance metric template by id
//      *
//      * @phase templateId id of template need to run
//      * @return {@link PMTemplateProcessResultObject}
//      */
//     @Operation(summary = "Run manually performance metric template by id")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "404", description = "Not Found")
//     })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
//             description = "Bearer Token type")
//     @ResponseStatus(HttpStatus.OK)
//     @PostMapping("/{template_id}/run")
//     ResponseEntity<PMTemplateProcessResultObject> runTemplate(
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Parameter(description = "Id of the report template will be run")
//             @Valid @NotNull(message = "templates.template_id_not_null")
//             @PathVariable("template_id")
//             Long templateId);

//     /**
//      * Retrieve all Analysis type supported for pm template
//      *
//      * @return ResponseEntity<AnalysisListResultObject>
//      */
//     @Operation(summary = "Retrieve all Analysis type supported for pm template")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "200", description = "Success")
//     })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
//             description = "Bearer Token type")
//     @GetMapping("/analysis-types")
//     ResponseEntity<AnalysisMetadataResultObject> getAllAnalysisTypes(@PathVariable(AGENCY_ID) Integer agencyId);

// }
