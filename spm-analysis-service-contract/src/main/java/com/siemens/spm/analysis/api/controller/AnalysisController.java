// /*
//  * Copyright (C) Siemens.  All Rights Reserved.
//  *
//  * Source      : AnalysisController.java
//  * Project     : SPM Platform
//  */
// package com.siemens.spm.analysis.api.controller;

// import java.time.LocalDate;
// import java.time.LocalDateTime;
// import java.time.LocalTime;

// import org.springframework.format.annotation.DateTimeFormat;
// import org.springframework.http.ResponseEntity;
// import org.springframework.web.bind.annotation.GetMapping;
// import org.springframework.web.bind.annotation.PathVariable;
// import org.springframework.web.bind.annotation.PostMapping;
// import org.springframework.web.bind.annotation.RequestBody;
// import org.springframework.web.bind.annotation.RequestMapping;
// import org.springframework.web.bind.annotation.RequestParam;

// import com.siemens.spm.analysis.api.vo.response.AnalysisMetadataResultObject;
// import com.siemens.spm.analysis.api.vo.response.AnalysisResultNonChartObject;
// import com.siemens.spm.analysis.api.vo.response.AnalysisResultObject;
// import com.siemens.spm.analysis.api.vo.response.MetricDayListResultObject;
// import com.siemens.spm.analysis.api.vo.response.PhaseFilterResultObject;
// import com.siemens.spm.analysis.vo.AogChartVO;
// import com.siemens.spm.analysis.vo.AorChartVO;
// import com.siemens.spm.analysis.vo.AppDelayChartVO;
// import com.siemens.spm.analysis.vo.CoordinationChartVO;
// import com.siemens.spm.analysis.vo.CoordinationHealthChartVO;
// import com.siemens.spm.analysis.vo.PedChartVO;
// import com.siemens.spm.analysis.vo.PtChartVO;
// import com.siemens.spm.analysis.vo.SplitFailureChartVO;
// import com.siemens.spm.analysis.vo.VolumeChartVO;
// import com.siemens.spm.analysis.vo.YellowTrapAnalysisVO;
// import com.siemens.spm.analysis.vo.abnormaldata.AbnormalDataChartVO;
// import com.siemens.spm.analysis.vo.moe.MOEAnalysisChartVO;
// import com.siemens.spm.analysis.vo.pp.PpChartVO;
// import com.siemens.spm.analysis.vo.queuelength.QueueLengthChartVO;
// import com.siemens.spm.analysis.vo.redlightviolation.RedLightViolationChartVO;
// import com.siemens.spm.analysis.vo.splitmonitor.SplitMonitorChartVO;
// import com.siemens.spm.analysis.vo.turningmovement.TurningMovementChartVO;
// import com.siemens.spm.common.api.PublicController;
// import com.siemens.spm.perflog.vo.PerfLogBundleVO;
// import io.swagger.v3.oas.annotations.Operation;
// import io.swagger.v3.oas.annotations.Parameter;
// import io.swagger.v3.oas.annotations.enums.ParameterIn;
// import io.swagger.v3.oas.annotations.responses.ApiResponse;
// import io.swagger.v3.oas.annotations.responses.ApiResponses;
// import io.swagger.v3.oas.annotations.tags.Tag;

// /**
//  * Common interface for public Analysis resources
//  */
// @Tag(name = "analysis", description = "Analysis Resources")
// @RequestMapping(AnalysisController.API_ROOT)
// public interface AnalysisController extends PublicController {

//     String VERSION = "/v1";
//     String ANALYSIS_RESOURCE = "/analysis";
//     String API_ROOT = PUBLIC_API + VERSION + AGENCY_RESOURCE + AGENCY_ID_RESOURCE + ANALYSIS_RESOURCE;
//     String CURRENT_AGENCY_RESOURCE = "/current-agency"; // TODO Use for current agency APIs
//     String INT_UUID = "int_uuid";
//     String INT_UUID_RESOURCE = "/{" + INT_UUID + "}";

//     String AOR_RESOURCE = "/aor";
//     String AOG_RESOURCE = "/aog";
//     String PT_RESOURCE = "/pt";
//     String COORD_RESOURCE = "/coord";
//     String SPLIT_FAILURE_RESOURCE = "/split";
//     String PED_RESOURCE = "/ped";
//     String COORD_HEALTH_RESOURCE = "/coord_health";
//     String APPROACH_DELAY_RESOURCE = "/app_delay";
//     String QUEUE_LENGTH_RESOURCE = "/queue_length";
//     String ABNORMAL_DATA_RESOURCE = "/abnormal_data";
//     String VOL_RESOURCE = "/vol";
//     String YT_RESOURCE = "/yellow_trap";
//     String SPLIT_MONITOR_RESOURCE = "/split_monitor";
//     String PP_ANALYSIS_RESOURCE = "/pp";
//     String TURNING_MOVEMENT_ANALYSIS_RESOURCE = "/turning_movement";

//     String RED_LIGHT_VIOLATION_ANALYSIS_RESOURCE = "/red_light_violation";

//     String MOE_ANALYSIS_RESOURCE = "/moe_analysis";

//     String METADATA_RESOURCE = ""; // TODO
//     String ANALYSIS_DAYS_RESOURCE = "/days"; // TODO

//     String GET_PHASE_FILTER_URL = "/{analysis_type}/{int_uuid}/phase-filter";

//     String DASHBOARD_RESOURCE = "/dashboard";

//     /**
//      * Get AoR Analysis from specific Agency data.</br>
//      * <p>
//      * GET /api/v1/analysis/{agency_uuid}/aor/{int_uuid}?from_time={from_time}&to_time={to_time}&bin_size={bin_size}
//      *
//      * @phase agencyId
//      * @phase intUUID
//      * @phase fromTime
//      * @phase toTime
//      * @return ResponseEntity<AnalysisResultObject < AorChartVO>>
//      */
//     @Operation(summary = "Get AoR Analysis from specific Agency data")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "400", description = "Bad Request or data is not available"),
//     })

//     @GetMapping(AOR_RESOURCE + INT_UUID_RESOURCE)
//     ResponseEntity<AnalysisResultObject<AorChartVO>> getAorAnalysis(
//             @Parameter(description = "Agency ID")
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Parameter(description = "Intersection UUID")
//             @PathVariable(INT_UUID) String intUUID,

//             @Parameter(description = "Beginning of Analysis time range")
//             @RequestParam(value = "from_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime fromTime,

//             @Parameter(description = "Ending of Analysis time range")
//             @RequestParam(value = "to_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime toTime,

//             @Parameter(description = "Volume bin size")
//             @RequestParam(value = "bin_size", required = false) Integer binSize
//     );

//     /**
//      * Only use for testing. Need to remove after that
//      */
//     @PostMapping(AOR_RESOURCE + INT_UUID_RESOURCE)
//     ResponseEntity<AnalysisResultObject<AorChartVO>> testGetAorAnalysis(
//             @Parameter(description = "Agency ID")
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Parameter(description = "Intersection UUID")
//             @PathVariable(INT_UUID) String intUUID,

//             @Parameter(description = "Beginning of Analysis time range")
//             @RequestParam(value = "from_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime fromTime,

//             @Parameter(description = "Ending of Analysis time range")
//             @RequestParam(value = "to_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime toTime,

//             @Parameter(description = "Volume bin size")
//             @RequestParam(value = "bin_size", required = false) Integer binSize,

//             @RequestBody PerfLogBundleVO perfLogBundleVO
//     );

//     /**
//      * Get AoG Analysis from specific Agency data.</br>
//      * <p>
//      * GET /api/v1/analysis/{agency_uuid}/aog/{int_uuid}?from_time={from_time}&to_time={to_time}&bin_size={bin_size}
//      *
//      * @phase agencyId
//      * @phase intUUID
//      * @phase fromTime
//      * @phase toTime
//      * @return ResponseEntity<AnalysisResultObject < AogChartVO>>
//      */
//     @Operation(summary = "Get AoG Analysis from specific Agency data")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "400", description = "Bad Request or data is not available"),
//     })
//     @GetMapping(AOG_RESOURCE + INT_UUID_RESOURCE)
//     ResponseEntity<AnalysisResultObject<AogChartVO>> getAogAnalysis(
//             @Parameter(description = "Agency ID")
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Parameter(description = "Intersection UUID")
//             @PathVariable(INT_UUID) String intUUID,

//             @Parameter(description = "Beginning of Analysis time range")
//             @RequestParam(value = "from_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime fromTime,

//             @Parameter(description = "Ending of Analysis time range")
//             @RequestParam(value = "to_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime toTime,

//             @Parameter(description = "Volume bin size")
//             @RequestParam(value = "bin_size", required = false) Integer binSize
//     );

//     /**
//      * Only use for testing. Need to remove after that
//      */
//     @PostMapping(AOG_RESOURCE + INT_UUID_RESOURCE)
//     ResponseEntity<AnalysisResultObject<AogChartVO>> testGetAogAnalysis(
//             @Parameter(description = "Agency ID")
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Parameter(description = "Intersection UUID")
//             @PathVariable(INT_UUID) String intUUID,

//             @Parameter(description = "Beginning of Analysis time range")
//             @RequestParam(value = "from_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime fromTime,

//             @Parameter(description = "Ending of Analysis time range")
//             @RequestParam(value = "to_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime toTime,

//             @Parameter(description = "Volume bin size")
//             @RequestParam(value = "bin_size", required = false) Integer binSize,

//             @RequestBody PerfLogBundleVO perfLogBundleVO
//     );

//     /**
//      * Get AoR Analysis from specific Agency data.</br>
//      * <p>
//      * GET /api/v1/analysis/{agency_uuid}/coord/{int_uuid}?from_time={from_time}&to_time={to_time}&bin_size={bin_size}
//      *
//      * @phase agencyId
//      * @phase intUUID
//      * @phase fromTime
//      * @phase toTime
//      * @return ResponseEntity<AnalysisResultObject < AorChartVO>>
//      */
//     @Operation(summary = "Get Coordination Analysis from specific Agency data")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "400", description = "Bad Request or data is not available"),
//     })
//     @GetMapping(COORD_RESOURCE + INT_UUID_RESOURCE)
//     ResponseEntity<AnalysisResultObject<CoordinationChartVO>> getCoordinationAnalysis(
//             @Parameter(description = "Agency ID")
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Parameter(description = "Intersection UUID")
//             @PathVariable(INT_UUID) String intUUID,

//             @Parameter(description = "Beginning of Analysis time range")
//             @RequestParam(value = "from_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime fromTime,

//             @Parameter(description = "Ending of Analysis time range")
//             @RequestParam(value = "to_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime toTime,

//             @Parameter(description = "Volume bin size")
//             @RequestParam(value = "bin_size", required = false) Integer binSize
//     );

//     /**
//      * Get PT Analysis from current Agency data.</br>
//      * <p>
//      * GET /api/v1/analysis/{agency_uuid}/pt/{int_uuid}?from_time={from_time}&to_time={to_time}
//      *
//      * @phase intUUID
//      * @phase fromTime
//      * @phase toTime
//      * @return ResponseEntity<AnalysisResultObject < PtChartVO>>
//      */
//     @Operation(summary = "Get PT Analysis from specific Agency data")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "400", description = "Bad Request or data is not available"),
//     })
//     @GetMapping(PT_RESOURCE + INT_UUID_RESOURCE)
//     ResponseEntity<AnalysisResultObject<PtChartVO>> getPtAnalysis(
//             @Parameter(description = "Agency ID")
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Parameter(description = "Intersection UUID")
//             @PathVariable(INT_UUID) String intUUID,

//             @Parameter(description = "Beginning of Analysis time range")
//             @RequestParam(value = "from_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime fromTime,

//             @Parameter(description = "Ending of Analysis time range")
//             @RequestParam(value = "to_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime toTime
//     );

//     /**
//      * Get Pedestrian Analysis from current Agency data.</br>
//      * <p>
//      * GET /api/v1/analysis/{agency_uuid}/ped/{int_uuid}?from_time={from_time}&to_time={to_time}
//      *
//      * @phase intUUID
//      * @phase fromTime
//      * @phase toTime
//      * @return ResponseEntity<AnalysisResultObject < PtChartVO>>
//      */
//     @Operation(summary = "Get Pedestrian Analysis from specific Agency data")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "400", description = "Bad Request or data is not available"),
//     })
//     @GetMapping(PED_RESOURCE + INT_UUID_RESOURCE)
//     ResponseEntity<AnalysisResultObject<PedChartVO>> getPedestrianAnalysis(
//             @Parameter(description = "Agency ID")
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Parameter(description = "Intersection UUID")
//             @PathVariable(INT_UUID) String intUUID,

//             @Parameter(description = "Beginning of Analysis time range")
//             @RequestParam(value = "from_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime fromTime,

//             @Parameter(description = "Ending of Analysis time range")
//             @RequestParam(value = "to_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime toTime
//     );

//     /**
//      * Retrieve all Analyses
//      *
//      * @return ResponseEntity<AnalysisListResultObject>
//      */
//     @Operation(summary = "Retrieve all Analyses")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "200", description = "Success")
//     })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
//             description = "Bearer Token type")
//     @GetMapping(METADATA_RESOURCE)
//     ResponseEntity<AnalysisMetadataResultObject> getAllAnalysisMetadata(@PathVariable(AGENCY_ID) Integer agencyId);

//     /**
//      * Get Analysis day preview data by metric/analysis
//      *
//      * @phase analysisID From AnalysisType
//      * @phase intUUID    Intersection UUID
//      * @phase fromDate   Beginning local date of preview data (inclusive)
//      * @phase toDate     Ending local date of preview data (inclusive)
//      * @return ResponseEntity<AnalysisListResultObject>
//      */
//     @Operation(summary = "Get Analysis day preview data by metric/analysis")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "200", description = "Success")
//     })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
//             description = "Bearer Token type")
//     @GetMapping(ANALYSIS_DAYS_RESOURCE)
//     ResponseEntity<MetricDayListResultObject> getAnalysisDays(
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Parameter(description = "Analysis ID")
//             @RequestParam(value = "analysis_id") String analysisID,

//             @Parameter(description = "Intersection UUID")
//             @RequestParam(value = INT_UUID) String intUUID,

//             @Parameter(description = "From date")
//             @RequestParam(value = "from_date")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fromDate,

//             @Parameter(description = "To date")
//             @RequestParam(value = "to_date")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate toDate
//     );

//     /**
//      * Get default phase filter for an Analysis
//      *
//      * @phase analysisType
//      * @phase intUUID
//      * @phase fromTime
//      * @phase toTime
//      * @return
//      */

//     @Operation(summary = "Get default phase filter for an Analysis")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "200",
//                     description = "Success. Null data indicating that phase filter does not apply.")
//     })
//     @GetMapping(GET_PHASE_FILTER_URL)
//     ResponseEntity<PhaseFilterResultObject> getPhaseFilter(
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Parameter(description = "Analysis type")
//             @PathVariable("analysis_type") String analysisType,

//             @Parameter(description = "Intersection UUID")
//             @PathVariable(INT_UUID) String intUUID,

//             @Parameter(description = "Beginning of Analysis time range")
//             @RequestParam(value = "from_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime fromTime,

//             @Parameter(description = "Ending of Analysis time range")
//             @RequestParam(value = "to_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime toTime
//     );

//     /**
//      * Get Coordination Health Analysis from specific Agency data
//      * <p>
//      * GET
//      * /api/v1/analysis/{agency_uuid}/coord_health/{int_uuid}?from_time={from_time}&to_time={to_time}&bin_size={bin_size}
//      *
//      * @phase agencyId
//      * @phase intUUID
//      * @phase fromTime
//      * @phase toTime
//      * @phase binSize
//      * @return
//      */

//     @Operation(summary = "Get Coordination Health Analysis from specific Agency data")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "400", description = "Bad Request or data is not available"),
//     })
//     @GetMapping(COORD_HEALTH_RESOURCE + INT_UUID_RESOURCE)
//     ResponseEntity<AnalysisResultObject<CoordinationHealthChartVO>> getCoordinationHealthAnalysis(
//             @Parameter(description = "Agency ID")
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Parameter(description = "Intersection UUID")
//             @PathVariable(INT_UUID) String intUUID,

//             @Parameter(description = "Begining of Analysis time range")
//             @RequestParam(value = "from_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime fromTime,

//             @Parameter(description = "Ending of Analysis time range")
//             @RequestParam(value = "to_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime toTime,

//             @Parameter(description = "Volume bin size")
//             @RequestParam(value = "bin_size", required = false) Integer binSize
//     );

//     /**
//      * Only use for testing. Need to remove after that
//      */
//     @PostMapping(COORD_HEALTH_RESOURCE + INT_UUID_RESOURCE)
//     ResponseEntity<AnalysisResultObject<CoordinationHealthChartVO>> testGetCoordHealthAnalysis(
//             @Parameter(description = "Agency UUID")
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Parameter(description = "Intersection UUID")
//             @PathVariable(INT_UUID) String intUUID,

//             @Parameter(description = "Beginning of Analysis time range")
//             @RequestParam(value = "from_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime fromTime,

//             @Parameter(description = "Ending of Analysis time range")
//             @RequestParam(value = "to_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime toTime,

//             @Parameter(description = "Volume bin size")
//             @RequestParam(value = "bin_size", required = false) Integer binSize,

//             @RequestBody PerfLogBundleVO perfLogBundleVO
//     );

//     /**
//      * Get Split Failure Analysis from specific Agency data
//      * <p>
//      * GET /api/v1/analysis/{agency_uuid}/split/{int_uuid}?from_time={from_time}&to_time={to_time}
//      *
//      * @phase agencyId
//      * @phase intUUID
//      * @phase fromTime
//      * @phase toTime
//      * @return
//      */
//     @Operation(summary = "Get Split Failure Analysis ")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "400", description = "Bad Request or data is not available"),
//     })
//     @GetMapping(SPLIT_FAILURE_RESOURCE + INT_UUID_RESOURCE)
//     ResponseEntity<AnalysisResultObject<SplitFailureChartVO>> getSplitFailureAnalysis(
//             @Parameter(description = "Agency Id")
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Parameter(description = "Intersection UUID")
//             @PathVariable(INT_UUID) String intUUID,

//             @Parameter(description = "Beginning of Analysis time range")
//             @RequestParam(value = "from_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime fromTime,

//             @Parameter(description = "Ending of Analysis time range")
//             @RequestParam(value = "to_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime toTime
//     );

//     /**
//      * Get Approach Delay Analysis from specific Agency data
//      * <p>
//      * GET
//      * /api/v1/analysis/{agency_uuid}/approach_delay/{int_uuid}?from_time={from_time}&to_time={to_time}&bin_size={bin_size}
//      *
//      * @phase agencyId
//      * @phase intUUID
//      * @phase fromTime
//      * @phase toTime
//      * @phase binSize
//      * @return
//      */
//     @Operation(summary = "Get Approach Delay Analysis ")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "400", description = "Bad Request or data is not available"),
//     })
//     @GetMapping(APPROACH_DELAY_RESOURCE + INT_UUID_RESOURCE)
//     ResponseEntity<AnalysisResultObject<AppDelayChartVO>> getAppDelayAnalysis(
//             @Parameter(description = "Agency UUID")
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Parameter(description = "Intersection UUID")
//             @PathVariable(INT_UUID) String intUUID,

//             @Parameter(description = "Beginning of Analysis time range")
//             @RequestParam(value = "from_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime fromTime,

//             @Parameter(description = "Ending of Analysis time range")
//             @RequestParam(value = "to_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime toTime,

//             @Parameter(description = "Volume bin size")
//             @RequestParam(value = "bin_size", required = false) Integer binSize
//     );

//     /**
//      * Only use for testing. Need to remove after that
//      */
//     @PostMapping(SPLIT_FAILURE_RESOURCE + INT_UUID_RESOURCE)
//     ResponseEntity<AnalysisResultObject<SplitFailureChartVO>> testSplitFailureAnalysis(
//             @Parameter(description = "Agency Id")
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Parameter(description = "Intersection UUID")
//             @PathVariable(INT_UUID) String intUUID,

//             @Parameter(description = "Beginning of Analysis time range")
//             @RequestParam(value = "from_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime fromTime,

//             @Parameter(description = "Ending of Analysis time range")
//             @RequestParam(value = "to_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime toTime,

//             @RequestBody PerfLogBundleVO perfLogBundleVO
//     );

//     /**
//      * Only use for testing. Need to remove after that
//      */
//     @PostMapping(APPROACH_DELAY_RESOURCE + INT_UUID_RESOURCE)
//     ResponseEntity<AnalysisResultObject<AppDelayChartVO>> testAppDelayAnalysis(
//             @Parameter(description = "Agency UUID")
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Parameter(description = "Intersection UUID")
//             @PathVariable(INT_UUID) String intUUID,

//             @Parameter(description = "Beginning of Analysis time range")
//             @RequestParam(value = "from_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime fromTime,

//             @Parameter(description = "Ending of Analysis time range")
//             @RequestParam(value = "to_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime toTime,

//             @Parameter(description = "Volume bin size")
//             @RequestParam(value = "bin_size", required = false) Integer binSize,

//             @RequestBody PerfLogBundleVO perfLogBundleVO
//     );

//     /**
//      * Get Queue Length Analysis from specific Agency data
//      * <p>
//      * GET: /api/v1/analysis/{agency_uuid}/queue_length/{int_uuid}?from_time={from_time}&to_time={to_time}&bin_size={
//      * bin_size}
//      *
//      * @phase agencyId
//      * @phase intUUID
//      * @phase fromTime
//      * @phase toTime
//      * @phase binSize
//      * @return
//      */
//     @GetMapping(QUEUE_LENGTH_RESOURCE + INT_UUID_RESOURCE)
//     ResponseEntity<AnalysisResultObject<QueueLengthChartVO>> getQueueLengthAnalysis(
//             @Parameter(description = "Agency Id")
//             @PathVariable(AGENCY_ID)
//             Integer agencyId,

//             @Parameter(description = "Intersection UUID")
//             @PathVariable(INT_UUID)
//             String intUUID,

//             @Parameter(description = "Beginning of Analysis time range")
//             @RequestParam(value = "from_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
//             LocalDateTime fromTime,

//             @Parameter(description = "Ending of Analysis time range")
//             @RequestParam(value = "to_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
//             LocalDateTime toTime,

//             @Parameter(description = "Volume bin size")
//             @RequestParam(value = "bin_size", required = false)
//             Integer binSize
//     );

//     /**
//      * Get Volume Analysis from specific Agency data.</br>
//      */
//     @GetMapping(VOL_RESOURCE + INT_UUID_RESOURCE)
//     ResponseEntity<AnalysisResultObject<VolumeChartVO>> getVolumeAnalysis(
//             @Parameter(description = "Agency Id")
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Parameter(description = "Intersection UUID")
//             @PathVariable(INT_UUID) String intUUID,

//             @Parameter(description = "Beginning of Analysis time range")
//             @RequestParam(value = "from_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime fromTime,

//             @Parameter(description = "Ending of Analysis time range")
//             @RequestParam(value = "to_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime toTime,

//             @Parameter(description = "Volume bin size")
//             @RequestParam(value = "bin_size", required = false) Integer binSize
//     );

//     /**
//      * Test Get Yellow Trap Analysis from specific Agency data.</br>
//      */
//     @PostMapping(YT_RESOURCE + INT_UUID_RESOURCE)
//     ResponseEntity<AnalysisResultNonChartObject<YellowTrapAnalysisVO>> testGetYellowTrapAnalysis(
//             @Parameter(description = "Agency ID")
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Parameter(description = "Intersection UUID")
//             @PathVariable(INT_UUID) String intUUID,

//             @Parameter(description = "Beginning of Analysis time range")
//             @RequestParam(value = "from_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime fromTime,

//             @Parameter(description = "Ending of Analysis time range")
//             @RequestParam(value = "to_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime toTime,

//             @RequestBody PerfLogBundleVO perfLogBundleVO
//     );

//     @GetMapping(ABNORMAL_DATA_RESOURCE + INT_UUID_RESOURCE)
//     ResponseEntity<AnalysisResultObject<AbnormalDataChartVO>> getAbnormalAnalysis(
//             @Parameter(description = "Agency UUID")
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Parameter(description = "Intersection UUID")
//             @PathVariable(INT_UUID) String intUUID,

//             @Parameter(description = "Target date")
//             @RequestParam(value = "target_date")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate targetDate,

//             @Parameter(description = "Days period")
//             @RequestParam(value = "day_period") Integer daysPeriod,

//             @Parameter(description = "Beginning of Analysis time range")
//             @RequestParam(value = "from_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.TIME) LocalTime fromTime,

//             @Parameter(description = "Ending of Analysis time range")
//             @RequestParam(value = "to_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.TIME) LocalTime toTime
//     );

//     @PostMapping(ABNORMAL_DATA_RESOURCE + INT_UUID_RESOURCE)
//     ResponseEntity<AnalysisResultObject<AbnormalDataChartVO>> testGetAbnormalAnalysis(
//             @Parameter(description = "Agency UUID")
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Parameter(description = "Intersection UUID")
//             @PathVariable(INT_UUID) String intUUID,

//             @Parameter(description = "Target date")
//             @RequestParam(value = "target_date")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate targetDate,

//             @Parameter(description = "Days period")
//             @RequestParam(value = "days_period") Integer daysPeriod,

//             @Parameter(description = "Beginning of Analysis time range")
//             @RequestParam(value = "from_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.TIME) LocalTime fromTime,

//             @Parameter(description = "Ending of Analysis time range")
//             @RequestParam(value = "to_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.TIME) LocalTime toTime,

//             @RequestBody PerfLogBundleVO perfLogBundleVO
//     );

//     /**
//      * Get Split Monitor Analysis from specific Agency data.</br>
//      */
//     @GetMapping(SPLIT_MONITOR_RESOURCE + INT_UUID_RESOURCE)
//     ResponseEntity<AnalysisResultObject<SplitMonitorChartVO>> getSplitMonitorAnalysis(
//             @Parameter(description = "Agency UUID")
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Parameter(description = "Intersection UUID")
//             @PathVariable(INT_UUID) String intUUID,

//             @Parameter(description = "Beginning of Analysis time range")
//             @RequestParam(value = "from_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime fromTime,

//             @Parameter(description = "Ending of Analysis time range")
//             @RequestParam(value = "to_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime toTime
//     );

//     /**
//      * Get Split Monitor Analysis from specific Agency data.</br>
//      */
//     @PostMapping(SPLIT_MONITOR_RESOURCE + INT_UUID_RESOURCE)
//     ResponseEntity<AnalysisResultObject<SplitMonitorChartVO>> testGetSplitMonitorAnalysis(
//             @Parameter(description = "Agency Id")
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Parameter(description = "Intersection UUID")
//             @PathVariable(INT_UUID) String intUUID,

//             @Parameter(description = "Beginning of Analysis time range")
//             @RequestParam(value = "from_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime fromTime,

//             @Parameter(description = "Ending of Analysis time range")
//             @RequestParam(value = "to_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime toTime,

//             @RequestBody PerfLogBundleVO perfLogBundleVO
//     );

//     /**
//      * Get Preemption/Priority Analysis for specific intersection in queried time range
//      * <p>
//      * GET: /api/v1/analysis/{agency_uuid}/pp/{int_uuid}?from_time={from_time}&to_time={to_time}
//      */
//     @Operation(summary = "Get Preemption/Priority Analysis ")
//     @GetMapping(PP_ANALYSIS_RESOURCE + INT_UUID_RESOURCE)
//     ResponseEntity<AnalysisResultObject<PpChartVO>> getPpAnalysis(
//             @Parameter(description = "Agency Id")
//             @PathVariable(AGENCY_ID)
//             Integer agencyId,

//             @Parameter(description = "Intersection UUID")
//             @PathVariable(INT_UUID)
//             String intUUID,

//             @Parameter(description = "Beginning of Analysis time range")
//             @RequestParam(value = "from_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
//             LocalDateTime fromTime,

//             @Parameter(description = "Ending of Analysis time range")
//             @RequestParam(value = "to_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
//             LocalDateTime toTime
//     );

//     /**
//      * Only for testing purpose
//      */
//     @Operation(summary = "Get Preemption/Priority Analysis ")
//     @PostMapping(PP_ANALYSIS_RESOURCE + INT_UUID_RESOURCE)
//     ResponseEntity<AnalysisResultObject<PpChartVO>> testGetPpAnalysis(
//             @Parameter(description = "Agency Id")
//             @PathVariable(AGENCY_ID)
//             Integer agencyId,

//             @Parameter(description = "Intersection UUID")
//             @PathVariable(INT_UUID)
//             String intUUID,

//             @Parameter(description = "Beginning of Analysis time range")
//             @RequestParam(value = "from_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
//             LocalDateTime fromTime,

//             @Parameter(description = "Ending of Analysis time range")
//             @RequestParam(value = "to_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
//             LocalDateTime toTime,

//             @RequestBody PerfLogBundleVO perfLogBundleVO
//     );

//     /**
//      * Get Turning Movement Analysis for specific intersection in queried time range
//      * <p>
//      * GET: /api/v1/analysis/{agency_uuid}/turning_movement/{int_uuid}?from_time={from_time}&to_time={to_time}
//      */
//     @Operation(summary = "Get Turning Movement Analysis")
//     @GetMapping(TURNING_MOVEMENT_ANALYSIS_RESOURCE + INT_UUID_RESOURCE)
//     ResponseEntity<AnalysisResultObject<TurningMovementChartVO>> getTurningMovementAnalysis(
//             @Parameter(description = "Agency UUID")
//             @PathVariable(AGENCY_ID)
//             Integer agencyId,

//             @Parameter(description = "Intersection UUID")
//             @PathVariable(INT_UUID)
//             String intUUID,

//             @Parameter(description = "Beginning of Analysis time range")
//             @RequestParam(value = "from_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
//             LocalDateTime fromTime,

//             @Parameter(description = "Ending of Analysis time range")
//             @RequestParam(value = "to_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
//             LocalDateTime toTime,

//             @Parameter(description = "Volume bin size")
//             @RequestParam(value = "bin_size", required = false)
//             Integer binSize
//     );

//     /**
//      * Get Red Light Violation Analysis for specific intersection in queried time range
//      * <p>
//      * GET: /api/v1/analysis/{agency_uuid}/red_light_violation/{int_uuid}?from_time={from_time}&to_time={to_time}
//      */
//     @Operation(summary = "Get Red Light Violation Analysis")
//     @GetMapping(RED_LIGHT_VIOLATION_ANALYSIS_RESOURCE + INT_UUID_RESOURCE)
//     ResponseEntity<AnalysisResultObject<RedLightViolationChartVO>> getRedLightViolationAnalysis(
//             @Parameter(description = "Agency ID")
//             @PathVariable(AGENCY_ID)
//             Integer agencyId,

//             @Parameter(description = "Intersection UUID")
//             @PathVariable("int_uuid")
//             String intUUID,

//             @Parameter(description = "Beginning of Analysis time range")
//             @RequestParam(value = "from_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
//             LocalDateTime fromTime,

//             @Parameter(description = "Ending of Analysis time range")
//             @RequestParam(value = "to_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
//             LocalDateTime toTime,

//             @Parameter(description = "Volume bin size")
//             @RequestParam(value = "bin_size", required = false)
//             Integer binSize
//     );

//     @Operation(summary = "Get MOE Analysis")
//     @GetMapping(MOE_ANALYSIS_RESOURCE + INT_UUID_RESOURCE)
//     ResponseEntity<AnalysisResultObject<MOEAnalysisChartVO>> getMOEAnalysis(
//             @Parameter(description = "Agency UUID")
//             @PathVariable(AGENCY_ID)
//             Integer agencyId,

//             @Parameter(description = "Intersection UUID")
//             @PathVariable("int_uuid")
//             String intUUID,

//             @Parameter(description = "Beginning of Analysis time range")
//             @RequestParam(value = "from_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
//             LocalDateTime fromTime,

//             @Parameter(description = "Ending of Analysis time range")
//             @RequestParam(value = "to_time")
//             @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
//             LocalDateTime toTime
//     );
// }


