package com.siemens.spm.analysis.api.controller;

import com.siemens.spm.analysis.api.vo.constraint.ValidDetectorTemplateSchedule;
import com.siemens.spm.analysis.api.vo.detectorreport.DetectorTemplateScheduleVO;
import com.siemens.spm.analysis.api.vo.enums.TemplateAggregation;
import com.siemens.spm.analysis.api.vo.enums.TemplateStatus;
import com.siemens.spm.analysis.api.vo.request.IntersectionIdsRequestVO;
import com.siemens.spm.analysis.api.vo.request.detectorreport.DetectorTemplateActivateRequestVO;
import com.siemens.spm.analysis.api.vo.request.detectorreport.DetectorTemplateCreateRequestVO;
import com.siemens.spm.analysis.api.vo.request.detectorreport.DetectorTemplateUpdateCoreDataRequestVO;
import com.siemens.spm.analysis.api.vo.response.IntersectionSearchResultObject;
import com.siemens.spm.analysis.api.vo.response.detectorreport.DetectorMetricResultObject;
import com.siemens.spm.analysis.api.vo.response.detectorreport.DetectorScheduleResultObject;
import com.siemens.spm.analysis.api.vo.response.detectorreport.DetectorTemplateCoreDataResultObject;
import com.siemens.spm.analysis.api.vo.response.detectorreport.DetectorTemplateDetailResultObject;
import com.siemens.spm.analysis.api.vo.response.detectorreport.DetectorTemplateManipulateResultObject;
import com.siemens.spm.analysis.api.vo.response.detectorreport.DetectorTemplateProcessResultObject;
import com.siemens.spm.analysis.api.vo.response.detectorreport.DetectorTemplateSearchResultObject;
import com.siemens.spm.common.api.PublicController;
import com.siemens.spm.common.constant.AgencyConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.time.DayOfWeek;
import java.util.List;

@Validated
@Tag(name = "detector-report-v2", description = "Detector Report Template Resources")
@RequestMapping(DetectorTemplateControllerV2.API_ROOT)
public interface DetectorTemplateControllerV2 extends PublicController {

    String VERSION = "/v2";
    String DETECTOR_REPORT_RESOURCES = "/detector-report";
    String TEMPLATE_RESOURCES = "/templates";
    String DETECTOR_REPORT_TEMPLATE_RESOURCES = DETECTOR_REPORT_RESOURCES + TEMPLATE_RESOURCES;
    String API_ROOT = PUBLIC_API + VERSION + DETECTOR_REPORT_TEMPLATE_RESOURCES;
    String TEMPLATE_BY_ID_RESOURCES = "/{template_id}";
    String INTERSECTION_RESOURCES = "/intersections";
    String SCHEDULE_RESOURCES = "/schedule";
    String ACTIVE_TEMPLATE_RESOURCES = "/activate";
    String TEMPLATE_INTERSECTIONS_RESOURCES = TEMPLATE_BY_ID_RESOURCES + INTERSECTION_RESOURCES;
    String TEMPLATES_INTERSECTIONS_RESOURCES = INTERSECTION_RESOURCES;
    String TEMPLATE_METRICS_RESOURCE = "/metrics";
    String TEMPLATE_SCHEDULE_RESOURCE = TEMPLATE_BY_ID_RESOURCES + SCHEDULE_RESOURCES;
    String RUN_MANUALLY_TEMPLATE_RESOURCES = TEMPLATE_BY_ID_RESOURCES + "/run";

    /**
     * Create new  detector report template: POST /api/v1/detector-report/templates
     *
     * @param requestVO {@link DetectorTemplateCreateRequestVO}
     * @return {@link DetectorTemplateDetailResultObject}
     */
    @Operation(summary = "Create a detector report template")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "400", description = "Bad Request")
    })
    @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
            description = "Bearer Token type")
    @ResponseStatus(HttpStatus.CREATED)
    @PostMapping
    ResponseEntity<DetectorTemplateDetailResultObject> create(
            @Parameter(name = AgencyConstants.AGENCY_ID_HEADER_V2, 
                       example = AgencyConstants.AGENCY_ID_HEADER_V2 + "=4246",
                       required = true,
                       in = ParameterIn.HEADER, description = "Agency Id")
            @RequestHeader(AgencyConstants.AGENCY_ID_HEADER_V2) Integer agencyId,
            
            @Parameter(description = "Core data of detector report template")
            @Valid @NotNull(message = "request_body_not_null")
            @RequestBody
            DetectorTemplateCreateRequestVO requestVO);

    @Operation(description = "Retrieve a list of intersections available to add into detector template depending on template_id")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "400", description = "Bad Request"),
            @ApiResponse(responseCode = "404", description = "Not Found"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
            description = "Bearer Token type")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping(TEMPLATES_INTERSECTIONS_RESOURCES + "/available")
    ResponseEntity<IntersectionSearchResultObject> searchAvailableTemplateIntersections(
            @Parameter(name = AgencyConstants.AGENCY_ID_HEADER_V2, 
                       example = AgencyConstants.AGENCY_ID_HEADER_V2 + "=4246",
                       required = true,
                       in = ParameterIn.HEADER, description = "Agency Id")
            @RequestHeader(AgencyConstants.AGENCY_ID_HEADER_V2) Integer agencyId,

            @Parameter(description = "Id of the detector template to search intersections")
            @RequestParam(value = "template_id", required = false)
            Long templateId,

            @Parameter(description = "Id of the intersection need to filter out")
            @RequestParam(value = "exclude_int_ids", required = false)
            String[] excludeIntIds,

            @Parameter(description = "It will be used to search intersections by name, email")
            @RequestParam(value = "text", required = false)
            String text,

            @Parameter(description = "Sort is used to order")
            @RequestParam(value = "sort", required = false)
            String[] sort,

            @Parameter(description = "Enable pagination or not")
            @RequestParam(value = "pagination", required = false, defaultValue = "false")
            Boolean pagination,

            @Parameter(description = "Page is used to page the returning result")
            @RequestParam(value = "page", required = false)
            Integer page,

            @Parameter(description = "Size is used to size the returning result")
            @RequestParam(value = "size", required = false)
            Integer size
    );

    /**
     * Get core data of a detector report template by id.
     *
     * @param templateId id of detector report template need to retrieve core data
     * @return {@link DetectorTemplateCoreDataResultObject}
     */
    @Operation(summary = "Retrieve a detector report template core data by id")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "404", description = "Not Found")
    })
    @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
            description = "Bearer Token type")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping(TEMPLATE_BY_ID_RESOURCES)
    ResponseEntity<DetectorTemplateCoreDataResultObject> getCoreData(
            @Parameter(name = AgencyConstants.AGENCY_ID_HEADER_V2, 
                       example = AgencyConstants.AGENCY_ID_HEADER_V2 + "=4246",
                       required = true,
                       in = ParameterIn.HEADER, description = "Agency Id")
            @RequestHeader(AgencyConstants.AGENCY_ID_HEADER_V2) Integer agencyId,
            
            @Parameter(description = "Id of the detector report template will be get")
            @Valid
            @NotNull(message = "templates.template_id_not_null")
            @PathVariable("template_id")
            Long templateId);

    /**
     * Get schedule data of a detector report template by id
     *
     * @param templateId id of template
     * @return {@link DetectorScheduleResultObject}
     */
    @Operation(summary = "Retrieve a detector template schedule data by id")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "404", description = "Not Found")
    })
    @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
            description = "Bearer Token type")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping(TEMPLATE_SCHEDULE_RESOURCE)
    ResponseEntity<DetectorScheduleResultObject> getTemplateScheduleData(
            @Parameter(name = AgencyConstants.AGENCY_ID_HEADER_V2, 
                       example = AgencyConstants.AGENCY_ID_HEADER_V2 + "=4246",
                       required = true,
                       in = ParameterIn.HEADER, description = "Agency Id")
            @RequestHeader(AgencyConstants.AGENCY_ID_HEADER_V2) Integer agencyId,
            
            @Parameter(description = "Id of the detector template will be get schedule data")
            @Valid @NotNull(message = "templates.template_id_not_null")
            @PathVariable("template_id")
            Long templateId);

    /**
     * Update core data of a detector report template by id
     *
     * @param templateId      id of template need to update core data
     * @param updateRequestVO {@link }
     * @return {@link DetectorTemplateManipulateResultObject}
     */
    @Operation(summary = "Update core data of a detector template by id")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "404", description = "Not Found")
    })
    @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
            description = "Bearer Token type")
    @ResponseStatus(HttpStatus.OK)
    @PutMapping(TEMPLATE_BY_ID_RESOURCES)
    ResponseEntity<DetectorTemplateManipulateResultObject> updateCoreData(
            @Parameter(name = AgencyConstants.AGENCY_ID_HEADER_V2, 
                       example = AgencyConstants.AGENCY_ID_HEADER_V2 + "=4246",
                       required = true,
                       in = ParameterIn.HEADER, description = "Agency Id")
            @RequestHeader(AgencyConstants.AGENCY_ID_HEADER_V2) Integer agencyId,
            
            @Parameter(description = "Id of the detector report template will be update core data")
            @Valid @NotNull(message = "templates.template_id_not_null")
            @PathVariable("template_id")
            Long templateId,

            @Parameter(description = "information about template to update")
            @Valid @NotNull(message = "request_body_not_null")
            @RequestBody DetectorTemplateUpdateCoreDataRequestVO updateRequestVO
    );

    /**
     * Update schedule data of a detector report template by id
     *
     * @param templateId         id of detector template need to update core data
     * @param templateScheduleVO {@link DetectorTemplateScheduleVO}
     * @return {@link DetectorTemplateManipulateResultObject}
     */
    @Operation(summary = "Update schedule data of a detector report template by id")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "404", description = "Not Found")
    })
    @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
            description = "Bearer Token type")
    @ResponseStatus(HttpStatus.OK)
    @PutMapping(TEMPLATE_SCHEDULE_RESOURCE)
    ResponseEntity<DetectorTemplateManipulateResultObject> updateTemplateScheduleData(
            @Parameter(name = AgencyConstants.AGENCY_ID_HEADER_V2, 
                       example = AgencyConstants.AGENCY_ID_HEADER_V2 + "=4246",
                       required = true,
                       in = ParameterIn.HEADER, description = "Agency Id")
            @RequestHeader(AgencyConstants.AGENCY_ID_HEADER_V2) Integer agencyId,
            
            @Parameter(description = "Id of the detector template will be update schedule data")
            @Valid @NotNull(message = "templates.template_id_not_null")
            @PathVariable("template_id")
            Long templateId,

            @Parameter(description = "information about schedule data to update")
            @Valid @NotNull(message = "request_body_not_null")
            @ValidDetectorTemplateSchedule(message = "templates.schedule.invalid")
            @RequestBody
            DetectorTemplateScheduleVO templateScheduleVO
    );

    /**
     * Delete multiple detector templates by list of template ids This method is SOFT delete
     *
     * @param templateIds {@link List <Long>} List of template ids need to delete
     * @return {@link DetectorTemplateManipulateResultObject} object
     */
    @Operation(summary = "Delete multiple detector templates")
    @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
            description = "Bearer Token type")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @DeleteMapping
    ResponseEntity<DetectorTemplateManipulateResultObject> deletes(
            @Parameter(name = AgencyConstants.AGENCY_ID_HEADER_V2, 
                       example = AgencyConstants.AGENCY_ID_HEADER_V2 + "=4246",
                       required = true,
                       in = ParameterIn.HEADER, description = "Agency Id")
            @RequestHeader(AgencyConstants.AGENCY_ID_HEADER_V2) Integer agencyId,
            
            @Parameter(description = "List of id of detector templates will be delete")
            @Valid @NotEmpty(message = "templates.template_ids_not_empty")
            @RequestParam(value = "template_ids")
            List<Long> templateIds
    );

    /**
     * Update intersection(s) to a specific detector template(delete or add depends on action):
     * <p>
     * PATCH: /detector-report/templates/{template_id}/intersections
     *
     * @param templateId               id of template need to update intersection(s)
     * @param intersectionIdsRequestVO {@link IntersectionIdsRequestVO} contain information about intersection(s) need
     *                                 to update
     * @return {@link DetectorTemplateManipulateResultObject}
     */
    @Operation(summary = "Update intersections to a specific detector template(delete or add depends on action)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "400", description = "Bad Request")
    })
    @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
            description = "Bearer Token type")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @PatchMapping(TEMPLATE_INTERSECTIONS_RESOURCES)
    ResponseEntity<DetectorTemplateManipulateResultObject> updateIntersections(
            @Parameter(name = AgencyConstants.AGENCY_ID_HEADER_V2, 
                       example = AgencyConstants.AGENCY_ID_HEADER_V2 + "=4246",
                       required = true,
                       in = ParameterIn.HEADER, description = "Agency Id")
            @RequestHeader(AgencyConstants.AGENCY_ID_HEADER_V2) Integer agencyId,
            
            @Parameter(description = "Id of a detector template that intersections will be update")
            @Valid @NotNull(message = "templates.template_id_not_null")
            @PathVariable("template_id")
            Long templateId,

            @Parameter(description = "List of ids of intersections to be update")
            @Valid @RequestBody
            IntersectionIdsRequestVO intersectionIdsRequestVO
    );

    /**
     * Update status of multiple detector report templates(ACTIVE or INACTIVE):
     * <p>
     * PATCH /detector-report/templates/activate
     *
     * @return {@link ResponseEntity<DetectorTemplateManipulateResultObject>}
     */
    @Operation(summary = "Activate multiple detector report templates")
    @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
            description = "Bearer Token type")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @PatchMapping(ACTIVE_TEMPLATE_RESOURCES)
    ResponseEntity<DetectorTemplateManipulateResultObject> activate(
            @Parameter(name = AgencyConstants.AGENCY_ID_HEADER_V2, 
                       example = AgencyConstants.AGENCY_ID_HEADER_V2 + "=4246",
                       required = true,
                       in = ParameterIn.HEADER, description = "Agency Id")
            @RequestHeader(AgencyConstants.AGENCY_ID_HEADER_V2) Integer agencyId,
            
            @Parameter(description = "List of ids of detector report templates to be update status.")
            @Valid
            @RequestBody
            DetectorTemplateActivateRequestVO activeRequestVO
    );

    /**
     * Retrieve a list of intersections depending on request phase
     *
     * @param templateId     id of template
     * @param text           text to search (name, email, ...)
     * @param status         status (ACTIVATE or INACTIVE)
     * @param orderByColumns order by columns
     * @param page           page
     * @param size           size in page
     * @return {@link IntersectionSearchResultObject}
     */
    @Operation(summary = "Retrieve a list of intersections depending on template_id")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "400", description = "Bad Request"),
            @ApiResponse(responseCode = "404", description = "Not Found"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
            description = "Bearer Token type")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping(TEMPLATE_INTERSECTIONS_RESOURCES)
    ResponseEntity<IntersectionSearchResultObject> searchTemplateIntersections(
            @Parameter(name = AgencyConstants.AGENCY_ID_HEADER_V2, 
                       example = AgencyConstants.AGENCY_ID_HEADER_V2 + "=4246",
                       required = true,
                       in = ParameterIn.HEADER, description = "Agency Id")
            @RequestHeader(AgencyConstants.AGENCY_ID_HEADER_V2) Integer agencyId,
            
            @Parameter(description = "Id of the detector template to search intersections")
            @Valid
            @NotNull(message = "templates.template_id_not_null")
            @PathVariable("template_id")
            Long templateId,

            @Parameter(description = "It will be used to search intersections by name, email")
            @RequestParam(value = "text", required = false)
            String text,

            @Parameter(description = "It will be used to search intersections by status")
            @RequestParam(value = "status", required = false)
            String status,

            @Parameter(description = "Fields will be used to sort returning data")
            @RequestParam(value = "sort", required = false)
            String[] orderByColumns,

            @Parameter(description = "Page is used to page the returning result")
            @RequestParam(value = "page", required = false)
            Integer page,

            @Parameter(description = "Size is used to size the returning result")
            @RequestParam(value = "size", required = false)
            Integer size,

            @Parameter(description = "Should Paginate is used to pagination result")
            @RequestParam(value = "pagination", required = false)
            Boolean pagination
    );

    /**
     * Search all detector report templates by filters, pagination and sorting
     *
     * @param agencyId       id of agency
     * @param text           text to search (name, description, ..)
     * @param fromDate       create time greater than fromDate
     * @param toDate         create time smaller than toDate
     * @param weekDays       weeks day of template(s)
     * @param aggregation    aggregation unit of template(s)
     * @param ownerId        id of owner of template(s)
     * @param status         status of template(s)
     * @param orderByColumns order by columns
     * @param page           page
     * @param size           size of page
     * @return {@link DetectorTemplateSearchResultObject}
     */
    @Operation(summary = "Get all detector report templates with filters, pagination and sorting")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "400", description = "Bad Request"),
            @ApiResponse(responseCode = "404", description = "Not Found"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
            description = "Bearer Token type")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping
    ResponseEntity<DetectorTemplateSearchResultObject> search(
            @Parameter(name = AgencyConstants.AGENCY_ID_HEADER_V2, 
                       example = AgencyConstants.AGENCY_ID_HEADER_V2 + "=4246",
                       required = true,
                       in = ParameterIn.HEADER, description = "Agency Id")
            @RequestHeader(AgencyConstants.AGENCY_ID_HEADER_V2) Integer agencyId,

            @Parameter(description = "Text to search")
            @RequestParam(value = "text", required = false)
            String text,

            @Parameter(description = "This day must be lower than created_at")
            @RequestParam(value = "from_date", required = false)
            Long fromDate,

            @Parameter(description = "This day must be greater than created_at")
            @RequestParam(value = "to_date", required = false)
            Long toDate,

            @Parameter(description = "Day of the week must be between MONDAY -> SUNDAY")
            @RequestParam(value = "week_days", required = false)
            DayOfWeek[] weekDays,

            @Parameter(description = "Aggregation unit of template")
            @RequestParam(value = "aggregation", required = false)
            TemplateAggregation aggregation,

            @Parameter(description = "Id of an owner that the template belong to")
            @RequestParam(value = "owner_id", required = false)
            Long ownerId,

            @Parameter(description = "The status of template")
            @RequestParam(value = "status", required = false)
            TemplateStatus status,

            @Parameter(description = "Fields will be used to sort returning data")
            @RequestParam(value = "sort", required = false)
            String[] orderByColumns,

            @Parameter(description = "Page is used to page the returning result")
            @RequestParam(value = "page", required = false)
            Integer page,

            @Parameter(description = "Size is used to size the returning result")
            @RequestParam(value = "size", required = false)
            Integer size
    );

    @Operation(summary = "Get all available metrics in templates")
    @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
            description = "Bearer Token type")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping(TEMPLATE_METRICS_RESOURCE)
    ResponseEntity<DetectorMetricResultObject> getAllMetrics(
            @Parameter(name = AgencyConstants.AGENCY_ID_HEADER_V2, 
                       example = AgencyConstants.AGENCY_ID_HEADER_V2 + "=4246",
                       required = true,
                       in = ParameterIn.HEADER, description = "Agency Id")
            @RequestHeader(AgencyConstants.AGENCY_ID_HEADER_V2) Integer agencyId
    );

    /**
     * Run manually a detector template by id
     *
     * @param templateId id of template need to run
     * @return {@link DetectorTemplateProcessResultObject}
     */
    @Operation(summary = "Run manually detector template by id")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "404", description = "Not Found")
    })
    @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
            description = "Bearer Token type")
    @ResponseStatus(HttpStatus.OK)
    @PostMapping(RUN_MANUALLY_TEMPLATE_RESOURCES)
    ResponseEntity<DetectorTemplateProcessResultObject> runTemplate(
            @Parameter(name = AgencyConstants.AGENCY_ID_HEADER_V2, 
                       example = AgencyConstants.AGENCY_ID_HEADER_V2 + "=4246",
                       required = true,
                       in = ParameterIn.HEADER, description = "Agency Id")
            @RequestHeader(AgencyConstants.AGENCY_ID_HEADER_V2) Integer agencyId,
            
            @Parameter(description = "Id of the detector template will be run")
            @Valid @NotNull(message = "templates.template_id_not_null")
            @PathVariable("template_id")
            Long templateId);
}
