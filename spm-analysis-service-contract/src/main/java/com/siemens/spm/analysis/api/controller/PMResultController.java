// package com.siemens.spm.analysis.api.controller;

// import java.util.List;

// import jakarta.validation.Valid;
// import jakarta.validation.constraints.NotEmpty;
// import jakarta.validation.constraints.NotNull;

// import org.springframework.http.HttpStatus;
// import org.springframework.http.ResponseEntity;
// import org.springframework.web.bind.annotation.DeleteMapping;
// import org.springframework.web.bind.annotation.GetMapping;
// import org.springframework.web.bind.annotation.PathVariable;
// import org.springframework.web.bind.annotation.RequestMapping;
// import org.springframework.web.bind.annotation.RequestParam;
// import org.springframework.web.bind.annotation.ResponseStatus;

// import com.siemens.spm.analysis.api.vo.response.performancemetric.PMDetailResultObject;
// import com.siemens.spm.analysis.api.vo.response.performancemetric.PMResultManipulateResultObject;
// import com.siemens.spm.analysis.api.vo.response.performancemetric.PMResultSearchResultObject;
// import com.siemens.spm.common.api.PublicController;
// import io.swagger.v3.oas.annotations.Operation;
// import io.swagger.v3.oas.annotations.Parameter;
// import io.swagger.v3.oas.annotations.enums.ParameterIn;
// import io.swagger.v3.oas.annotations.responses.ApiResponse;
// import io.swagger.v3.oas.annotations.responses.ApiResponses;
// import io.swagger.v3.oas.annotations.tags.Tag;

// @Tag(name = "performance-metric-result", description = "Performance Metric Result Resources")
// @RequestMapping(PMResultController.API_ROOT)
// public interface PMResultController extends PublicController {

//     String VERSION = "/v1";
//     String PM_RESOURCES = "/performance-metric";
//     String PM_RESULT_RESOURCES = PM_RESOURCES + "/results";
//     String API_ROOT = PUBLIC_API + VERSION + AGENCY_RESOURCE + AGENCY_ID_RESOURCE + PM_RESULT_RESOURCES;

//     /**
//      * Retrieve all Performance metric report results
//      *
//      * @return {@code ResponseEntity<PerformanceMetricResultObject>}
//      */
//     @Operation(summary = "Retrieve all performance metric results")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "200", description = "Successful"),
//             @ApiResponse(responseCode = "404", description = "Not Found")
//     })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
//             description = "Bearer Token type")
//     @ResponseStatus(HttpStatus.OK)
//     @GetMapping
//     ResponseEntity<PMResultSearchResultObject> search(
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Parameter(description = "Text to search")
//             @RequestParam(value = "text", required = false)
//             String text,

//             @Parameter(description = "id of metric type")
//             @RequestParam(value = "metric_type", required = false)
//             String metricTypeId,

//             @Parameter(description = "Id of an owner that the report belong to")
//             @RequestParam(value = "owner_id", required = false)
//             Long ownerId,

//             @Parameter(description = "Date that filter data by from date")
//             @RequestParam(value = "from_date", required = false)
//             Long fromDate,

//             @Parameter(description = "Date that filter data by to date")
//             @RequestParam(value = "to_date", required = false)
//             Long toDate,

//             @Parameter(description = "Fields will be used to sort returning data")
//             @RequestParam(value = "sort", required = false)
//             String[] orderByColumns,

//             @Parameter(description = "Page is used to page the returning result")
//             @RequestParam(value = "page", required = false)
//             Integer page,

//             @Parameter(description = "Size is used to size the returning result")
//             @RequestParam(value = "size", required = false)
//             Integer size
//     );

//     /**
//      * Retrieve the performance metric result details
//      *
//      * @return {@code ResponseEntity<PMDetailResultObject>}
//      */
//     @Operation(summary = "Retrieve the performance metric result details")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "200", description = "success"),
//             @ApiResponse(responseCode = "404", description = "Not Found")
//     })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true,
//             in = ParameterIn.HEADER, description = "Bearer Token type")
//     @ResponseStatus(HttpStatus.OK)
//     @GetMapping("/{id}")
//     ResponseEntity<PMDetailResultObject> getPMResultDetail(
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Parameter(description = "The id of performance metric result")
//             @Valid @NotNull(message = "performance_metric.results.id_not_null")
//             @PathVariable("id") Long resultId);

//     /**
//      * Soft delete result record
//      *
//      * @phase ids contains List of result id to delete
//      * @return {@code PMResultManipulateResultObject} object
//      */
//     @Operation(summary = "Delete performance metric results")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "200", description = "Successful"),
//             @ApiResponse(responseCode = "404", description = "Not Found")
//     })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER,
//             description = "Bearer Token type")
//     @ResponseStatus(HttpStatus.OK)
//     @DeleteMapping()
//     ResponseEntity<PMResultManipulateResultObject> deletes(
//             @PathVariable(AGENCY_ID) Integer agencyId,

//             @Valid @NotEmpty(message = "performance_metric.results.result_ids_not_empty")
//             @RequestParam(value = "result_ids") List<Long> ids
//     );

// }
