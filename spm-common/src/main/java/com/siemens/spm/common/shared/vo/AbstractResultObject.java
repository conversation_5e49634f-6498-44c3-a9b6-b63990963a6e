/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AbstractResultObject.java
 * Project     : SPM Platform
 */
package com.siemens.spm.common.shared.vo;

import java.io.Serializable;
import java.text.MessageFormat;
import java.util.Collection;
import java.util.Map;

import org.springframework.http.HttpStatus;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.siemens.spm.common.util.TypeUtils;
import io.swagger.v3.oas.annotations.media.Schema;

import static io.swagger.v3.oas.annotations.media.Schema.AccessMode.READ_ONLY;

/**
 * Base class to create custom result objects to be used when return values over RESTful services. <br/>
 *
 * @param <T_VALUE>       the value we  want to hold in your response.
 * @param <S_STATUS_CODE> an enumeration that represents your result status, such as "SUCCESS, ERROR", etc.
 */
@JsonIgnoreProperties({ "statusCode", "message", "httpStatus", "args" })
public abstract class AbstractResultObject<T_VALUE extends Serializable, S_STATUS_CODE extends Serializable>
        extends AbstractSimpleResultObject<S_STATUS_CODE> {

    private static final long serialVersionUID = -5098768756655306147L;

    /**
     * "Default" ERROR Constructor
     */
    protected AbstractResultObject() {
        setData(null);
        setStatusCode(getErrorStatusValue());
    }

    /**
     * "Default" Success Constructor
     */
    protected AbstractResultObject(T_VALUE value) {
        setData(value);
        setStatusCode(getSuccessfulStatusValue());
    }

    /**
     * Secondary constructor to set status to other than success
     */
    protected AbstractResultObject(T_VALUE value, S_STATUS_CODE statusCode) {
        setData(value);
        setStatusCode(statusCode);
    }

    /**
     * This method is used to get the payload data
     */
    public abstract T_VALUE getData();

    /**
     * This method is used to get the status code
     */
    @Schema(accessMode = READ_ONLY)
    public abstract S_STATUS_CODE getStatusCode();

    /**
     * This method is used to get the message
     */
    @Schema(accessMode = READ_ONLY)
    public abstract String getMessage();

    /**
     * This method is used to get the HttpStatus
     */
    @Schema(accessMode = READ_ONLY)
    public abstract HttpStatus getHttpStatus();

    /**
     * This method is used to get the ErrorField name
     */
    @Schema(accessMode = READ_ONLY)
    public abstract String getErrorFieldName();

    /**
     * Internal set function for serialization, do not use in application code, instead use CTOR!
     */
    protected abstract void setData(T_VALUE value);

    /**
     * Internal set function for serialization, do not use in application code, instead use CTOR!
     */
    protected abstract void setStatusCode(S_STATUS_CODE value);

    /**
     * @return which value in the enumeration represents an error and will be set if no data can be provided in the
     * result
     */
    @JsonIgnore
    protected abstract S_STATUS_CODE getErrorStatusValue();

    /**
     * @return Which value in the enumeration of status values indicates success
     */
    @JsonIgnore
    public abstract S_STATUS_CODE getSuccessfulStatusValue();

    /**
     * Test if current result object contain a "successful" status flag.
     *
     * @return if current result is considered a success or not, true if success!
     */
    @JsonIgnore
    @Override
    public boolean isSuccessful() {
        return getSuccessfulStatusValue() == getStatusCode();
    }

    @JsonIgnore
    public Object[] getArgs() {
        return new Object[0];
    }

    /**
     * @return complete message for log output generation only
     */
    @Override
    @JsonIgnore
    public String toString() {
        // Define out put format, we do NOT trace the message parameters
        // (maybe too much stuff)
        String format = "{0}: statusCode=<{1}>; dataType=<{2}>; data=<{3}>";

        // Get class name
        String className = this.getClass().getSimpleName();

        // Get data information
        String dataType = null;
        Object dataInfo = null;
        if (null != getData()) {
            dataType = getData().getClass().getName();
            if (getData().getClass().isArray()) {
                dataInfo = "length: " + ((Object[]) getData()).length;
            } else if (getData() instanceof Collection<?>) {
                dataInfo = "size: " + ((Collection<?>) getData()).size();
            } else if (getData() instanceof Map<?, ?>) {
                dataInfo = "size: " + ((Map<?, ?>) getData()).size();
            } else {
                dataInfo = getData();
            }
        }

        return MessageFormat.format(format, className, getStatusCode(), dataType, dataInfo);
    }

    @Override
    public boolean equals(Object obj) {
        if (obj instanceof AbstractResultObject<?, ?> other) {
            return (TypeUtils.equals(this.getData(), other.getData())
                    && (TypeUtils.equals(this.getStatusCode(), other.getStatusCode())));
        }
        return false;
    }

    @Override
    public int hashCode() {
        return TypeUtils.hashCode(this.getData(), this.getStatusCode());
    }

}
